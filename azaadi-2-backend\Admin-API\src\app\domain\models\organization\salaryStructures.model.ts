import { model, Model, models, Schema } from "mongoose";
import { ISalaryStructure } from "../../interfaces/organization/salaryStructure.interface";
import { EMSSalaryStructureCategory } from "../../../../utils/meta/enum.utils";

/**
 * SalaryStructures model class that defines the schema and provides access to the SalaryStructures collection.
 * 
 * @class SalaryStructures
 * @description Handles the schema definition and model creation for Salary Structures
 */
export default class SalaryStructures {
    /**
     * Mongoose schema definition for SalaryStructures
     * @private
     */
    private static _schema = new Schema<ISalaryStructure>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                required: [true, "User ID is required"],
                trim: true,
                ref: "login",
                index: true // Indexed for faster lookups by user
            },
            tSalaryStructure: {
                type: Schema.Types.Mixed
            },
            eType: {
                type: String,
                enum: EMSSalaryStructureCategory,
                required: [true, "Salary structure type is required"],
                index: true // Indexed for filtering by salary structure type
            },
            bHavingAdvancePayment: {
                type: Boolean,
                default: false,
                index: true // Indexed for filtering by advance payment status
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for SalaryStructures
     * @private
     */
    private static _model: Model<ISalaryStructure> = models?.SalaryStructures || model<ISalaryStructure>("salarystructure", SalaryStructures._schema);

    /**
     * Get the Mongoose model for SalaryStructures
     * @returns {Model<ISalaryStructure>} The SalaryStructures model
     */
    public static get model(): Model<ISalaryStructure> {
        return SalaryStructures._model;
    }
}