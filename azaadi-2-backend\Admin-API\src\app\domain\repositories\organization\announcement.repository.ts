import { IAnnouncement } from "../../interfaces/organization/calendar.interface";
import { Announcement } from "../../models";
import Repository from "../repository";
import IAnnouncementRepository from "./abstracts/announcementRepository.abstract";

export default class AnnouncementRepository extends Repository<IAnnouncement> implements IAnnouncementRepository {
    constructor() {
        super(Announcement.model);
    }
}