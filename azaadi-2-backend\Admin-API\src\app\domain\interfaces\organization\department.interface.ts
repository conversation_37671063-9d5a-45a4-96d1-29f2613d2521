import { Types } from "mongoose";
import { IConcept } from "../concept.interface";
import { IOrganization } from "./organization.interface";
import { IRole } from "./role.interface";

export interface IDepartment extends IConcept {
    sName: string;
    tOrganization: string | Types.ObjectId | IOrganization;
    tDepartmentHead?: string | Types.ObjectId | IRole;
    sTag?: string;
}

export interface IDesignation extends IConcept {
    sName: string;
    sTag?: string;
    tOrganization:  string | Types.ObjectId | IOrganization;
}