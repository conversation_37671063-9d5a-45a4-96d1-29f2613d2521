import { Component } from '@angular/core';
import { BreadcrumbComponent } from '@shared/components';
import { ITreeNode } from '@shared/models';
import { IAction } from '@shared/models/Action';
import { IColumn } from '@shared/models/Column';
import { filteredResult } from '@shared/services/search/dynamic-search';
import { MenuItem } from 'primeng/api';

@Component({
  selector: 'app-employee',
  imports: [BreadcrumbComponent],
  templateUrl: './employee.component.html',
})

/**
 * Employee<PERSON>omponent manages the employee-related view and sets up breadcrumb navigation.
 */
export class EmployeeComponent {
  /**
   * The breadcrumb items displayed in the navigation.
   * Each item follows the `MenuItem` structure from PrimeNG.
   */
  breadCrumbItems: MenuItem[] = [
    { icon: 'home' },
    { label: 'Employee', route: '/employee' },
  ];
  selectedFilter = '';

  employeeCols: IColumn[] = [
    { field: 'email', header: 'Email' },
    { field: 'name', header: 'Name' },
    { field: 'employeeCode', header: 'Employee Code' },
    { field: 'phone', header: 'Phone' },
    { field: 'role', header: 'Role' },
    { field: 'organization', header: 'Organization' },
    { field: 'joinedDate', header: 'Joined' },
  ];

  employeeData = [
    {
      email: '<EMAIL>',
      name: 'John Doe',
      employeeCode: '12345',
      phone: '************',
      role: 'Manager',
      organization: 'ABC Company',
      joinedDate: '2022-01-01',
    },
    {
      email: '<EMAIL>',
      name: 'Jane Smith',
      employeeCode: '67890',
      phone: '************',
      role: 'Developer',
      organization: 'XYZ Corporation',
      joinedDate: '2022-02-15',
    },
    {
      email: '<EMAIL>',
      name: 'Bob Johnson',
      employeeCode: '24680',
      phone: '************',
      role: 'Designer',
      organization: 'PQR Industries',
      joinedDate: '2022-03-01',
    },
    {
      email: '<EMAIL>',
      name: 'Alice Brown',
      employeeCode: '13579',
      phone: '************',
      role: 'Tester',
      organization: 'LMN Enterprises',
      joinedDate: '2022-04-10',
    },
    {
      email: '<EMAIL>',
      name: 'Charlie Wilson',
      employeeCode: '86420',
      phone: '************',
      role: 'Manager',
      organization: 'OPQ Corporation',
      joinedDate: '2022-05-05',
    },
    {
      email: '<EMAIL>',
      name: 'Emily Davis',
      employeeCode: '75310',
      phone: '************',
      role: 'Developer',
      organization: 'QRS Industries',
      joinedDate: '2022-06-20',
    },
  ];

  actions: IAction[] = [
    { label: 'Add Employee', action: () => {} },
    { label: 'Export', action: () => {} },
    { label: 'Import Employee', action: () => {} },
    { label: 'Download template', action: () => {} },
  ];

  statusOptions = [
    {
      key: 1,
      value: 'Active',
    },
    {
      key: 0,
      value: 'Inactive',
    },
  ];

  filteredData = [...this.employeeData];
  filters = [...this.employeeCols];
  activeStatus: number = 1;

  onFilterSelected(filter: string) {
    this.selectedFilter = filter;
  }

  onSearchUpdated(searchText: string) {
    this.filteredData = filteredResult(
      this.filters,
      this.employeeData,
      this.selectedFilter,
      searchText
    );
  }
  treeViewData: ITreeNode[] = [
    {
      id: 1,
      label: 'Root 1',
      expanded: false,
      name: 'John Doe',
      designation: 'CEO',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
      children: [
        {
          id: 3,
          label: 'Child 1.1',
          expanded: false,
          name: 'Alice Smith',
          designation: 'Manager',
          image: 'https://randomuser.me/api/portraits/women/2.jpg',
          children: [
            {
              id: 7,
              label: 'Child 1.1.1',
              expanded: false,
              name: 'Michael Johnson',
              designation: 'Team Lead',
              image: 'https://randomuser.me/api/portraits/men/3.jpg',
              children: [
                {
                  id: 14,
                  label: 'Child 1.1.1.1',
                  expanded: false,
                  name: 'Emma Brown',
                  designation: 'Senior Developer',
                  image: 'https://randomuser.me/api/portraits/women/4.jpg',
                },
                {
                  id: 15,
                  label: 'Child 1.1.1.2',
                  expanded: false,
                  name: 'Liam Wilson',
                  designation: 'Developer',
                  image: 'https://randomuser.me/api/portraits/men/5.jpg',
                },
              ],
            },
            {
              id: 8,
              label: 'Child 1.1.2',
              expanded: false,
              name: 'Sophia Davis',
              designation: 'UI/UX Designer',
              image: 'https://randomuser.me/api/portraits/women/6.jpg',
            },
            {
              id: 9,
              label: 'Child 1.1.3',
              expanded: false,
              name: 'Noah Martinez',
              designation: 'QA Engineer',
              image: 'https://randomuser.me/api/portraits/men/7.jpg',
            },
          ],
        },
        {
          id: 4,
          label: 'Child 1.2',
          expanded: false,
          name: 'Olivia Garcia',
          designation: 'HR Manager',
          image: 'https://randomuser.me/api/portraits/women/8.jpg',
          children: [
            {
              id: 10,
              label: 'Child 1.2.1',
              expanded: false,
              name: 'William Taylor',
              designation: 'HR Executive',
              image: 'https://randomuser.me/api/portraits/men/9.jpg',
            },
            {
              id: 11,
              label: 'Child 1.2.2',
              expanded: false,
              name: 'Isabella Anderson',
              designation: 'Recruiter',
              image: 'https://randomuser.me/api/portraits/women/10.jpg',
            },
            {
              id: 12,
              label: 'Child 1.2.3',
              expanded: false,
              name: 'James Thomas',
              designation: 'Legal Advisor',
              image: 'https://randomuser.me/api/portraits/men/11.jpg',
            },
            {
              id: 13,
              label: 'Child 1.2.4',
              expanded: false,
              name: 'Mia White',
              designation: 'Finance Manager',
              image: 'https://randomuser.me/api/portraits/women/12.jpg',
            },
          ],
        },
      ],
    },
    {
      id: 2,
      label: 'Root 2',
      expanded: false,
      name: 'Benjamin Harris',
      designation: 'CTO',
      image: 'https://randomuser.me/api/portraits/men/13.jpg',
      children: [
        {
          id: 5,
          label: 'Child 2.1',
          expanded: false,
          name: 'Charlotte Clark',
          designation: 'Product Manager',
          image: 'https://randomuser.me/api/portraits/women/14.jpg',
        },
        {
          id: 6,
          label: 'Child 2.2',
          expanded: false,
          name: 'Ethan Rodriguez',
          designation: 'Software Architect',
          image: 'https://randomuser.me/api/portraits/men/15.jpg',
        },
        {
          id: 16,
          label: 'Child 2.3',
          expanded: false,
          name: 'Amelia Lewis',
          designation: 'Marketing Head',
          image: 'https://randomuser.me/api/portraits/women/16.jpg',
        },
      ],
    },
  ];
}
