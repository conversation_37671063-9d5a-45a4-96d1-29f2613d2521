import { Component, OnInit, signal, ViewChild } from '@angular/core';
import { CardModule } from 'primeng/card';
import { AttendanceStatus } from '../../../../models/Attendance';
import { CommonModule } from '@angular/common';
import { DatePickerModule } from 'primeng/datepicker';
import { ButtonModule } from 'primeng/button';
import { DividerModule } from 'primeng/divider';
import { IAttendanceReport } from '../../../../models/attendance-report';
import { AttendanceReportClassesPipe } from '../../../../pipes/attendance-report-classes.pipe';
import { AttendaceSymbolColorPipe } from '../../../../pipes/attendace-symbol-color.pipe';
import { Popover, PopoverModule } from 'primeng/popover';
import { DateSelectComponent } from '../date-select/date-select.component';
import { TranslatePipe } from '@ngx-translate/core';
const MONTH_NAMES = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];
const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
@Component({
  selector: 'app-attendance-report',
  imports: [
    CardModule,
    CommonModule,
    DatePickerModule,
    ButtonModule,
    DividerModule,
    AttendanceReportClassesPipe,
    AttendaceSymbolColorPipe,
    PopoverModule,
    DateSelectComponent,
    TranslatePipe,
  ],
  templateUrl: './attendance-report.component.html',
  styleUrl: './attendance-report.component.scss',
})
export class AttendanceReportComponent implements OnInit {
  @ViewChild('datePopover') datePopover!: Popover;
  attendanceStatus = AttendanceStatus;
  currentYear: number = 0;
  currentMonth: number = 0;
  calendarDates: (Date | null)[] = [];
  showSelector = false;
  yearOptions: number[] = [];
  attendanceReport = signal<IAttendanceReport>({
    report: [
      {
        calendarDates: new Date(new Date().setDate(new Date().getDate() - 1)),
        status: AttendanceStatus.PRESENT,
      },
      {
        calendarDates: new Date(new Date().setDate(new Date().getDate() + 1)),
        status: AttendanceStatus.Leave,
      },
      {
        calendarDates: new Date(new Date().setDate(new Date().getDate() + 2)),
        status: AttendanceStatus.ERROR,
      },
      {
        calendarDates: new Date(new Date().setDate(new Date().getDate() + 3)),
        status: AttendanceStatus.PUBLIC_HOLIDAY,
      },
      {
        calendarDates: new Date(new Date().setDate(new Date().getDate() + 4)),
        status: AttendanceStatus.ORGANIZATION_WEEKEND,
      },
      {
        calendarDates: new Date(new Date().setDate(new Date().getDate() + 5)),
        status: AttendanceStatus.HD,
      },
      {
        calendarDates: new Date(new Date().setDate(new Date().getDate() + 6)),
        status: AttendanceStatus.LHD,
      },
    ],
  });

  daysOfWeek: string[] = DAYS_OF_WEEK;
  months: string[] = MONTH_NAMES;

  ngOnInit() {
    const today = new Date();
    this.currentYear = today.getFullYear();
    this.currentMonth = today.getMonth();
    this.generateYearOptions();
    this.generateCalendar();
  }

  generateCalendar() {
    this.calendarDates = [];
    const firstDay = new Date(this.currentYear, this.currentMonth, 1);
    const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);

    const startDay = firstDay.getDay();
    const totalDays = lastDay.getDate();

    for (let i = 0; i < startDay; i++) {
      this.calendarDates.push(null);
    }

    for (let i = 1; i <= totalDays; i++) {
      this.calendarDates.push(new Date(this.currentYear, this.currentMonth, i));
    }
  }

  previousMonth() {
    this.currentMonth--;
    if (this.currentMonth < 0) {
      this.currentMonth = 11;
      this.currentYear--;
    }
    this.generateCalendar();
  }

  nextMonth() {
    this.currentMonth++;
    if (this.currentMonth > 11) {
      this.currentMonth = 0;
      this.currentYear++;
    }
    this.generateCalendar();
  }

  getCurrentSelectedDate(): Date {
    return new Date(this.currentYear, this.currentMonth, 1);
  }

  isToday(date: Date | null): boolean {
    if (!date) return false;
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  }

  toggleSelector() {
    this.showSelector = !this.showSelector;
  }

  generateYearOptions() {
    const currentYear = new Date().getFullYear();
    for (let i = currentYear - 10; i <= currentYear + 10; i++) {
      this.yearOptions.push(i);
    }
  }

  goToTodayDate() {
    const today = new Date();
    this.currentYear = today.getFullYear();
    this.currentMonth = today.getMonth();
    this.generateCalendar();
  }

  setSelectedDate(date: Date) {
    this.currentYear = date.getFullYear();
    this.currentMonth = date.getMonth();
    this.generateCalendar();
    this.datePopover.hide(); // Hide the popover after selecting a date
  }

  getBackgroundColor(date: Date | null): AttendanceStatus | undefined {
    if (!date) return;

    const key = this.attendanceReport().report.find(
      (item) => item.calendarDates?.toDateString() === date.toDateString()
    )?.status;

    if (new Date().toDateString() === date.toDateString()) {
      return AttendanceStatus.TODAYS_DATE;
    }

    return key;
  }
}
