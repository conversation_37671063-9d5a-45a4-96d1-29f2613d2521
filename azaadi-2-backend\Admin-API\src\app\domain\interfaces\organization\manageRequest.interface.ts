import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { IRole } from './role.interface';
import { EMSAdvanceStatus, EMSDeductType } from '../../../../utils/meta/enum.utils';
import { IOrganization } from './organization.interface';
import { IUser } from '../user/user.interface';

export interface IManageRoleRequest extends IConcept {
    tRole: string | Types.ObjectId | IRole;
    aCount: number;
    sType?: string;
    tApprovedBy?: string | Types.ObjectId | IRole;
}

export interface IManageUserRequest extends IConcept {
    tIdUser: string | Types.ObjectId | IUser;
    tType: string | Types.ObjectId | IManageRoleRequest;
    aCount: number;
    sDescription?: string;
    tRole?: string | Types.ObjectId | IRole;
}

export interface ILeaveBalance {
    aCount?: number;
    sType?: string;
    sRoleId?: string;
}

export interface IAdvanceMonthlyDetail extends IConcept {
    aMonth: number;
    aYear: number;
    aAmount: number;
    bIsDeducted?: boolean;
}

export interface IAttachment extends IConcept {
    sFilename: string;
    sPath: string;
}

export interface IAdvanceRequest extends IConcept {
    tIdUser: string | Types.ObjectId | IUser;
    aTotalAmount?: number;
    eStatus?: EMSAdvanceStatus;
    eDeductType?: EMSDeductType;
    dStartDate?: Date;
    dEndDate?: Date;
    monthlyDetails?: IAdvanceMonthlyDetail[];
    tHrApprovedBy?: string | Types.ObjectId | IUser;
    tAccApprovedBy?: string | Types.ObjectId | IUser;
    dDisbursementDate?: Date;
    dApproveDate?: Date;
    sReason?: string;
    sReply?: string;
    tAttachments?: IAttachment[];
    tOrganization?: string | Types.ObjectId | IOrganization;
    aDisbursementMonth?: number;
    aDisbursementYear?: number;
}

