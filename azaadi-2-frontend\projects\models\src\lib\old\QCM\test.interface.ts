import { EmsQuestion } from './question.interface';
import { EmsUser } from '../user.interface';
export interface EmsTest {
    _id?: string;
    sName: string;
    bIsRandomize?: boolean;
    bResultImmediately?: boolean;
    sDescription?: string;
    tQuestions?: EmsQuestion[] | string[];
    tUser?: EmsUser | String;
    createdAt?: Date;
    updatedAt?: Date;
    sTag?: string;
    sMetaData?: any[];
    aTestTotal?: number;
    aPassMark?: number;
}
