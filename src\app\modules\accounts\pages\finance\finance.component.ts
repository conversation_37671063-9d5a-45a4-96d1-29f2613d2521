import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TabMenuModule } from 'primeng/tabmenu';
import { IdentityInfoComponent } from './components/identity-info/identity-info.component';
import { InfoCardComponent } from './components/info-card/info-card.component';
import { IdentityInfo } from './models/identity-info';
import { InfoCardInput } from './models/info-card-input';

@Component({
  selector: 'app-finance',
  imports: [
    CommonModule,
    CardModule,
    ButtonModule,
    TabMenuModule,
    InfoCardComponent,
    IdentityInfoComponent,
  ],
  templateUrl: './finance.component.html',
  styleUrl: './finance.component.scss',
})
export class FinanceComponent {
  paymentInfo: InfoCardInput = {
    title: 'Payment Information',
    values: [
      {
        label: 'SALARY PAYMENT MODE',
        value: 'Bank Transfer',
        valueHexColor: '#FF0000',
      },
      { label: 'BANK NAME', value: 'HDFC', valueHexColor: '#FF0000' },
      {
        label: 'BANKING NAME',
        value: 'Sayan Sanfui',
        valueHexColor: '#FF0000',
      },
      {
        label: 'ACCOUNT NUMBER',
        value: '**************',
        valueHexColor: '#FF0000',
      },
      { label: 'IFSC CODE', value: 'HDF0001', valueHexColor: '#FF0000' },
    ],
  };

  pfAccountInfo: InfoCardInput = {
    title: 'PF Account Information',
    values: [
      { label: 'State', value: 'West Bengal', valueHexColor: '#FF0000' },
      { label: 'NAME OF THE ACCOUNT', value: 'HDFC', valueHexColor: '#FF0000' },
      {
        label: 'EFFECTIVE DATE',
        value: '01 May 2021',
        valueHexColor: '#FF0000',
      },
      { label: 'PT NUMBER', value: '**************', valueHexColor: '#FF0000' },
      {
        label: 'DATE OF REGISTRATION',
        value: '01 May 2021',
        valueHexColor: '#FF0000',
      },
    ],
  };

  ptAccount: InfoCardInput = {
    title: 'PT Account',
    values: [
      { label: 'State', value: 'West Bengal', valueHexColor: '#FF0000' },
      {
        label: 'REGISTERED LOCATION',
        value: 'Kolkata',
        valueHexColor: '#FF0000',
      },
    ],
  };

  identityInfo: InfoCardInput = {
    title: 'Identity Information',
    values: [
      { label: 'PAN CARD', value: 'EXAMPLE1234X', valueHexColor: '#00C853' },
      {
        label: 'PAN HOLDER NAME',
        value: 'John Wong',
        valueHexColor: '#FF0000',
      },
      {
        label: 'AADHAAR CARD',
        value: 'XXXX XXXX 1234',
        valueHexColor: '#00C853',
      },
      { label: 'NAME', value: 'John Wong', valueHexColor: '#FF0000' },
      {
        label: 'ENROLLMENT NUMBER',
        value: '1234/56789/01234',
        valueHexColor: '#FF0000',
      },
      { label: 'DOB', value: '01 Jan 1990', valueHexColor: '#FF0000' },
    ],
  };

  addressProof: InfoCardInput = {
    title: 'Address Proof',
    values: [
      { label: 'AADHAAR CARD', value: 'Valid', valueHexColor: '#00C853' },
      { label: 'NAME', value: 'John Wong', valueHexColor: '#FF0000' },
      { label: 'ADDRESS LINE', value: 'Kolkata', valueHexColor: '#FF0000' },
    ],
  };

  panCardInfo: IdentityInfo[] = [
    {
      title: 'Pan Card',
      icon: 'https://img.icons8.com/?size=100&id=ulwAzQmud0ql&format=png&color=000000',
      tag: 'Verified',
      tagType: 'success',
      noOfCols: 2,
      values: [
        {
          label: 'Permanent Account Number',
          value: 'XXXX929',
          valueHexColor: '#3b7180',
        },
        { label: 'Name', value: 'John Weak', valueHexColor: '#3b7180' },
        {
          label: 'Date of Birth',
          value: '02 Mar,2025',
          valueHexColor: '#3b7180',
        },
        {
          label: "Parent's Name",
          value: 'John Strong',
          valueHexColor: '#3b7180',
        },
      ],
    },
    {
      title: 'Aadhar Card',
      icon: 'https://img.icons8.com/?size=100&id=ulwAzQmud0ql&format=png&color=000000',
      tag: 'Verified',
      tagType: 'success',
      noOfCols: 2,
      values: [
        {
          label: 'Permanent Account Number',
          value: 'XXXX929',
          valueHexColor: '#3b7180',
        },
        { label: 'Name', value: 'John Weak', valueHexColor: '#3b7180' },
        {
          label: 'Date of Birth',
          value: '02 Mar,2025',
          valueHexColor: '#3b7180',
        },
        {
          label: "Parent's Name",
          value: 'John Strong',
          valueHexColor: '#3b7180',
        },
      ],
    },
  ];
  addressProofInfo: IdentityInfo[] = [
    {
      title: 'Aadhar Card',
      icon: 'https://img.icons8.com/?size=100&id=ulwAzQmud0ql&format=png&color=000000',
      tag: 'Verified',
      tagType: 'success',
      noOfCols: 2,
      values: [
        {
          label: 'Permanent Account Number',
          value: 'XXXX929',
          valueHexColor: '#3b7180',
        },
        { label: 'Name', value: 'John Weak', valueHexColor: '#3b7180' },
        {
          label: 'Date of Birth',
          value: '02 Mar,2025',
          valueHexColor: '#3b7180',
        },
        {
          label: "Parent's Name",
          value: 'John Strong',
          valueHexColor: '#3b7180',
        },
      ],
    },
  ];
}
