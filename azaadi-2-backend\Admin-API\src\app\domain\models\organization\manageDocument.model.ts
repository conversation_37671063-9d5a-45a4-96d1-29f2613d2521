import { model, Model, models, Schema } from 'mongoose';
import { IManageDocument } from '../../interfaces/organization/manageDocument.interface';
import { EMSFileType } from '../../../../utils/meta/enum.utils';

/**
 * ManageDocument model class that defines the schema and provides access to the ManageDocument collection.
 * 
 * @class ManageDocument
 * @description Handles the schema definition and model creation for Document Management
 */
export default class ManageDocument {
    /**
     * Mongoose schema definition for ManageDocument
     * @private
     */
    private static _schema: Schema<IManageDocument> = new Schema<IManageDocument>(
        {
            sName: {
                type: String,
                required: [true, "Name is missing!"],
                trim: true,
                index: true // Indexed for faster search and filtering by document name
            },
            sTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by document tag
            },
            eType: {
                type: [String],
                enum: Object.values(EMSFileType),
                default: [EMSFileType.PDF]
            },
            bAllowMultiple: {
                type: Boolean,
                default: false
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                required: [true, "Organization is required"],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for ManageDocument
     * @private
     */
    private static _model: Model<IManageDocument> = models?.incentivedetail || model<IManageDocument>("incentivedetail", ManageDocument._schema);

    /**
     * Get the Mongoose model for ManageDocument
     * @returns {Model<IManageDocument>} The ManageDocument model
     */
    public static get model(): Model<IManageDocument> {
        return ManageDocument._model;
    }
}