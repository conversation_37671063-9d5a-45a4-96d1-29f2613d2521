import { EmsAnnouncementType, EmsFileType, EmsLeaveType } from '../enums/managements.enum';
import { EmsUser } from './user.interface';

interface General {
  _id?: string;
  sName: string;
  sTag?: string;
  createdAt?: Date;
  updatedAt?: Date;
}
export interface EmsBranches extends General {
  tOrganization: string | EmsOrganization
}


export interface EmsDepartments extends General {
  tOrganization: string | EmsOrganization
  tDepartmentHead: string | EmsRole;
}

export interface EmsDesignation extends General {
  tOrganization: string | EmsOrganization
}

export interface EmsManageAccess extends General {
  tRole: EmsRole | string;
  bCanView: boolean;
  bCanWrite: boolean;
}
export interface EmsManageAssets extends General { }
export interface EmsManageDocuments extends General {
  eType: EmsFileType[];
  bAllowMultiple?: boolean;
  tOrganization?: EmsOrganization | string;
}

export interface EmsManageLeave {
  _id?: string;
  tIdUser: string | EmsUser;
  eType: EmsLeaveType;
  aCount: number;
  sDescription?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsCalendarEvent extends General {
  sDescription?: string;
  sUrl?: string;
  dStartDate: Date;
  dEndDate?: Date;
  bEveryYear?: boolean;
  tCalenderTemplate: EmsCalendarTemplate;
}

export interface EmsManageLogs {
  _id?: string;
  tIdUser: string | EmsUser;
  createdAt: Date;
  sMetaKey: string[];
  sMetaValue: string[];
}

export interface EmsOrganization extends General { }

export interface EmsOrganizationSetting {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  tOrganization: string | EmsOrganization;
  sOrgTag: string,
  sTitle: string,
  sValue: any,
  sType: string
}

export interface EmsRole extends General {
  tOrganization: EmsOrganization | string;
  tHead: EmsRole | string;
  tHrReport: EmsRole | string;
  tAdditionalOrg: EmsOrganization[] | string[];
}

export interface EmsRoleSettings {
  _id?: string;
  tRole: EmsRole | string;
  sTitle: string;
  sType: string;
  sValue: any;
  tManagedBy?: EmsRole | string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsTree {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  tIdUser?: string | EmsUser;
  tParentNode?: string | EmsTree;
  tChildNode: string[] | EmsTree[];
  tDepartment?: string | EmsDepartments;
  bIsHead?: boolean;
  sColor?: string;
}

export interface EmsBiometric {
  _id?: string;
  txnId: string,
  dvcId: string,
  dvcIP: string,
  txnDateTime: string,
  punchId: string,
  mode: string,
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsCalendarTemplate {
  _id?: string;
  sName: string;
  sTag: string;
  tOrganization: EmsOrganization | string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsManageRoleRequest {
  _id?: string,
  tRole: EmsRole | string,
  sType: string,
  tApprovedBy: EmsRole | string,
  aCount: number,
  createdAt?: Date,
  updatedAt?: Date,
}

export interface EmsManageUserRequest {
  _id?: string,
  tIdUser: EmsUser | string,
  tType: EmsManageRoleRequest | string,
  aCount: number,
  sDescription: string,
  tRole: EmsRole | string,
  createdAt?: Date,
  updatedAt?: Date,
}
export interface EmsAnnouncement extends EmsAnnouncementGeneric {
  _id?: string,
  createdAt?: Date,
  updatedAt?: Date,
  dStartDate?: Date,
  dEndDate?: Date,
  tOrganization?: EmsOrganization | string,
}
export interface EmsAnnouncementGeneric {
  sName?: string,
  sDescription?: string,
  sUrl?: string,
  eType?: EmsAnnouncementType,
}
export interface EmsPrivacyPolicy extends General {
  sContent?: string;
  tRole?: string | EmsRole;
  tOrganization: string | EmsOrganization;
}
export interface EmsHrPolicy extends EmsPrivacyPolicy { }

export interface EmsShift extends General {
  sTimezone?: string
  sTime?: string
  isDefault?: boolean
  sPunchInTime?: string
  sPunchOutTime?: string
  sTimeBuffer?: string // In hours
  tOrganization?: string | EmsOrganization
}
export interface EmsMailTemplate extends General {
  sBody?: string;
  tOrganization?: string | EmsOrganization;
}