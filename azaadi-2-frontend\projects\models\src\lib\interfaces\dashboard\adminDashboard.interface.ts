import { ICommonMonthCount } from "../common.interface";

export interface EmsAttendanceReport extends Pick<ICommonMonthCount,'aAmount'> {
    aTotalDays: number;
    aWeekends: number;
    aBusinessDays: number;
    aHolidays: number;
    aWorkingDays: number;
    aLeaves: number;
    aPresentDay: number;
}
export interface EmsEmployeeReport extends Pick<ICommonMonthCount,'aAmount'> {
    aNewJoin: number;
    aTotalLeft: number;
}