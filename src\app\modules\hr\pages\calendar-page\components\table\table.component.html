<div class="pt-4 bg-[var(--p-table-header-background)] rounded-xl shadow-md">
  <!-- Dropdown Header -->
  <div class="flex justify-end mb-2 px-4">
    <p-dropdown
      [options]="['Last 7 Days', 'Last 30 Days']"
      [(ngModel)]="range"
      class="text-sm"
      [style]="{ width: '150px' }"
    ></p-dropdown>
  </div>

  <div class="rounded-b-xl shadow-md overflow-hidden flex justify-around">
    <p-table
      [rows]="10"
      [showCurrentPageReport]="true"
      [value]="attendanceData"
      class="p-datatable-sm !border-0 w-full"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="header">
        <tr class="bg-gray-100 text-sm text-gray-700">
          <th><span class="pi pi-calendar"></span> Date</th>
          <th><span class="pi pi-stopwatch"></span> Check in & out</th>
          <th><span class="pi pi-clock"></span> Over & Less time</th>
          <th><span class="pi pi-hourglass"></span> Effective Hours</th>
          <th><span class="pi pi-home"></span> Mode</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-row class="!border-0">
        <tr class="text-sm !border-0">
          <td class="whitespace-nowrap font-medium !border-0">
            {{ row.date }}
            <span
              *ngIf="row.label"
              class="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-600 rounded-md border border-blue-200"
            >
              {{ row.label }}
            </span>
          </td>

          <td class="!border-0">
            <app-checkin-time [checkin]="row.checkin" [duration]="row.duration" [checkout]="row.checkout"></app-checkin-time>
          </td>

          <td class="!border-0">{{ row.overTime || "—" }}</td>

          <td class="!border-0">{{ row.effectiveHours || "—" }}</td>

          <td class="!border-0">
            <span
              *ngIf="row.mode"
              class="text-xs px-2 py-0.5 rounded-md border font-semibold"
              [ngClass]="{
                'bg-blue-100 text-blue-700 border-blue-300': row.mode === 'WFO',
                'bg-orange-100 text-orange-700 border-orange-300':
                  row.mode === 'WFH'
              }"
            >
              {{ row.mode }}
            </span>
            <span *ngIf="!row.mode">—</span>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
