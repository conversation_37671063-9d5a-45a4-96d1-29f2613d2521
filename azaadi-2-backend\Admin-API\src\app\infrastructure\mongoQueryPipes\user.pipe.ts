import { PopulateOptions, ProjectionType } from "mongoose"
import { IUser } from "../../domain/interfaces/user/user.interface"

export const userAuthenticateResponseProjectionPipe: ProjectionType<IUser> = {
    sEmail: 1,
    tIdEmployee: 1,
    aPhoneNumber: 1,
    sProfileUrl: 1,
    sWorkingType: 1,
    tOrganizations: 1,
    tRole: 1,
    tDepartment: 1,
    tDesignation: 1,
    tUserDetails: 1,
    tShift: 1,
    bIsActive: 1,
    bCanLogin: 1,
    bOnlyOfficePunch: 1,
    bIsResigned: 1,
    bIsCreatedBySuperAdmin: 1,
    bIsPermanentWFH: 1,
    sPassword: 1
}

export const userAuthenticateResponsePopulatePipe: PopulateOptions = {
    path: 'tRole tDepartment tDesignation tUserDetails tShift tOrganizations tIdEmployee',
    select: '_id sName sCode sTag bIsActive tOrganization tDepartmentHead sTimezone sTime sPunchInTime sPunchOutTime aPunchId sTimeBuffer isDefault',
    strictPopulate: false,
    populate: {
        path: 'tDepartmentHead',
        select: '_id sName sTag tOrganization',
        strictPopulate: false
    }
}