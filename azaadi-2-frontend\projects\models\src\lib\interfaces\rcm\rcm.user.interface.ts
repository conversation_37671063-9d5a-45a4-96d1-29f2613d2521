export interface EmsRcmUser {
    _id?: string;
    sPassword?: string;
    sEmail?: string;
    aPhoneNumber?: number;
    sWorkingType?: string;
    sProfileUrl?: string;
    tUserDetails?: EmsRcmUserDetails;
    createdAt?: Date;
    updatedAt?: Date;
    bIsActive?: Boolean;
    bCanLogin?: Boolean;
  }
  export interface EmsRcmUserDetails {
    _id?: string;
    sName: string;
    tDepartment?: any;
    tDesignation?: any;
    aAltPhoneNo?: number;
    sPersonalEmail?: string;
    sGender?: string;
    dDob?: Date;
    dJoinDate?: Date;
    sPresentAddress?: string;
    sPermanentAddress?: string;
    sFatherName?: string;
    aFatherMobileNo?: number;
    aAadharNo?: number;
    sPanNo?: string;
    createdAt?: Date;
    updatedAt?: Date;
    tEducationInfo?: EmsRcmEducationInfo[];
    tExperienceInfo?: EmsRcmExperienceInfo[];
    sPassportNo?: string;
    sPassportExpDate?: Date;
    sNationality?: string;
    sReligion?: string;
    sMaritalStatus?: string;
    sEmploymentSpouse?: string;
    aNoChildren?: number;
    bIsActive?: Boolean;
    sOrganization?: string;
    sUserApplyType?: string;
    metaData?: string[];
  }
  
  export interface EmsRcmEducationInfo {
    _id?: string;
    sName: string;
    sCourse: string;
    dStartDate: Date;
    bPursuing?: boolean;
    dEndDate?: Date;
    createdAt?: Date;
    updatedAt?: Date;
  }
  
  export interface EmsRcmExperienceInfo {
    _id?: string;
    sName: string;
    sDepartment?: string;
    sDesignation?: string;
    sAddress?: string;
    bCurrent?: boolean;
    dStartDate: Date;
    dEndDate?: Date;
    createdAt?: Date;
    updatedAt?: Date;
  }
  