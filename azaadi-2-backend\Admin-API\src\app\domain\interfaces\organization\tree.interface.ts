import { IConcept } from "../concept.interface";
import { Types } from "mongoose";
import { IDepartment } from "./department.interface";

export interface ITree extends IConcept {
    tIdUser: string | Types.ObjectId;
    tParentNode: string | Types.ObjectId | null;
    tChildNode: string[] | Types.ObjectId[];
    tDepartment: string | Types.ObjectId | IDepartment;
    bIsHead: boolean;
    sColor?: string;
}