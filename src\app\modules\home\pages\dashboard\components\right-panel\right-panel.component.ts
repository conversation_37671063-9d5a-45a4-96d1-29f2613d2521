import { Component } from '@angular/core';
import { CardModule } from 'primeng/card';
import { TabsModule } from 'primeng/tabs';
import { EditorModule } from 'primeng/editor';
import { FormsModule } from '@angular/forms';
import { PostComponent } from './components/post/post.component';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { IPost, IUser } from '@shared/models';
import { UpcomingHolidaysComponent } from './components/upcoming-holidays/upcoming-holidays.component';
import { UpcomingBirthdaysComponent } from './components/upcoming-birthdays/upcoming-birthdays.component';
import { AnniversayComponent } from './components/anniversay/anniversay.component';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
  selector: 'app-right-panel',
  imports: [
    CardModule,
    TabsModule,
    EditorModule,
    FormsModule,
    PostComponent,
    CommonModule,
    PanelModule,
    UpcomingHolidaysComponent,
    UpcomingBirthdaysComponent,
    AnniversayComponent,
    TranslatePipe,
  ],
  templateUrl: './right-panel.component.html',
  styleUrl: './right-panel.component.scss',
})
export class RightPanelComponent {
  postContent: string = '';
  onLeave: IUser[] = [
    {
      name: 'John Doe',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'Jane Smith',
      image:
        'https://www.shutterstock.com/image-photo/young-business-man-standing-close-260nw-1714914373.jpg',
    },
    {
      name: 'Bob Johnson',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'Alice Williams',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'Charlie Brown',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'Eve Green',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'George White',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
  ];
  posts: IPost[] = [
    {
      user: {
        name: 'Chandan Shaw',
        image:
          'https://www.shutterstock.com/image-photo/head-shot-portrait-close-smiling-600nw-1714666150.jpg',
      },
      body: 'Hello gys how are you. This is a post regarding the fun friday. Anyone who want to participate please comment p',
      date: new Date(),
    },
    {
      user: {
        name: 'Rahul Das',
        image:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      },
      body: 'Hello gys how are you. This is a post regarding the fun friday. Anyone who want to participate please comment p',
      date: new Date(),
    },
    {
      user: {
        name: 'Vikas Das',
        image:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      },
      body: 'Hello gys how are you. This is a post regarding the fun friday. Anyone who want to participate please comment p',
      date: new Date(),
    },
    {
      user: {
        name: 'Shivam Choubey',
        image:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      },
      body: 'Hello gys how are you. This is a post regarding the fun friday. Anyone who want to participate please comment p',
      date: new Date(),
    },
  ];
}
