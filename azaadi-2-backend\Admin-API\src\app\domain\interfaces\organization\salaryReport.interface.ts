import { Types } from 'mongoose';
import { IOrganization } from './organization.interface';
import { IConcept } from '../concept.interface';

export interface IDifferentSalarySheet extends IConcept {
    aMonth: number;
    aYear: number;
    aTotalAmount: number;
    aAmountDifference?: number;
    aTotalEmployees?: number;
    tEmployeesJoin?: string[]|Types.ObjectId[];
    tEmployeesLeft?: string[]|Types.ObjectId[];
    tApprovedBy?: string|Types.ObjectId;
    tOrganization: string|Types.ObjectId|IOrganization;
}

export interface ISalary extends IConcept {
    tSalary: Record<string, any>;
    dStartDate?: Date;
    dEndDate?: Date;
}

export interface ISalaryHistory extends IConcept {
    tIdUser: string|Types.ObjectId;
    tSalaries: ISalary[];
    tOrganization: string|Types.ObjectId|IOrganization;
}