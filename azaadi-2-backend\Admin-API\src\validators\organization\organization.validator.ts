import { NextFunction, Request, Response } from "express";
import { body, ValidationChain, validationResult } from "express-validator";

/**
 * Class to handle organization-related request validations
 */
export class OrganizationValidator {
    /**
     * Get validation rules for organization creation/update
     */
    public static getOrganizationValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Organization name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Organization name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Organization name must be between 2 and 100 characters"),

            // Organization tag validation
            body("sTag")
                .trim()
                .notEmpty()
                .withMessage("Organization tag is required")
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Organization tag can only contain letters, numbers, hyphens and underscores")
                .isLength({ min: 2, max: 50 })
                .withMessage("Organization tag must be between 2 and 50 characters"),

            // Super admin flag validation
            body("bIsCreatedBySuperAdmin")
                .optional()
                .isBoolean()
                .withMessage("IsCreatedBySuperAdmin must be a boolean"),

            // Active status validation
            body("bIsActive")
                .optional()
                .isBoolean()
                .withMessage("IsActive must be a boolean"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for organization settings
     */
    public static getOrganizationSettingsValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Organization ID validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Organization tag validation (optional)
            body("sOrgTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Organization tag can only contain letters, numbers, hyphens and underscores"),

            // Setting title validation
            body("sTitle")
                .notEmpty()
                .withMessage("Setting title is required")
                .isString()
                .withMessage("Setting title must be a string"),

            // Setting value validation (required but can be any type)
            body("sValue")
                .exists()
                .withMessage("Setting value is required"),

            // Setting type validation (optional)
            body("sType")
                .optional()
                .isString()
                .withMessage("Setting type must be a string"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for organization branch
     */
    public static getOrganizationBranchValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Branch name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Branch name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Branch name must be between 2 and 100 characters"),

            // Branch tag validation (optional)
            body("sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Branch tag can only contain letters, numbers, hyphens and underscores"),

            // Organization ID validation (optional in interface but might be required in business logic)
            body("tOrganization")
                .optional()
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}