import { model, Model, models, Schema, Types } from "mongoose";
import { IAttachment, IDateRange, IUserRequest } from "../../interfaces/user/request.interface";
import { EMSRequestStatus } from "../../../../utils/meta/enum.utils";

/**
 * UserRequest model class that defines the schema and provides access to the UserRequest collection.
 * 
 * @class UserRequest
 * @description Handles the schema definition and model creation for User Requests
 */
export default class UserRequest {
    /**
     * Mongoose schema definition for attachments in user requests
     * @private
     */
    private static readonly _attachmentSchema = new Schema<IAttachment>(
        {
            sFileName: {
                type: String,
                trim: true
            },
            sFilePath: {
                type: String,
                trim: true
            }
        },
        {
            timestamps: true,
            _id: true // Maintain separate IDs for attachments
        }
    );

    /**
     * Mongoose schema definition for date ranges in user requests
     * @private
     */
    private static readonly _dateRangeSchema = new Schema<IDateRange>(
        {
            dStartDate: {
                type: Date,
                required: [true, "Start date is required"],
                index: true // Indexed for date-based queries
            },
            dEndDate: {
                type: Date,
                required: [true, "End date is required"],
                index: true // Indexed for date-based queries
            }
        },
        {
            timestamps: true,
            _id: true // Maintain separate IDs for date ranges
        }
    );

    /**
     * Mongoose schema definition for UserRequest
     * @private
     */
    private static readonly _schema = new Schema<IUserRequest>(
        {
            tSender: {
                type: Schema.Types.ObjectId,
                required: [true, "User must be required!"],
                ref: 'login',
                trim: true,
                index: true // Indexed for faster lookups by sender
            },
            dStartDate: {
                type: Date,
                required: [true, "Start date is required!"],
                trim: true,
                index: true // Indexed for date-based queries
            },
            dEndDate: {
                type: Date,
                required: [true, "End date is required!"],
                trim: true,
                index: true // Indexed for date-based queries
            },
            tType: {
                type: Schema.Types.ObjectId,
                trim: true,
                ref: "manageUserRequest",
                index: true // Indexed for request type filtering
            },
            tApprovedBy: {
                type: Schema.Types.ObjectId,
                ref: 'login',
                trim: true,
                index: true // Indexed for faster lookups by approver
            },
            eStatus: {
                type: String,
                enum: EMSRequestStatus,
                index: true // Indexed for status filtering
            },
            sReason: {
                type: String,
                trim: true
            },
            sMessage: {
                type: String,
                trim: true
            },
            sReply: {
                type: String,
                trim: true
            },
            tRecipients: {
                type: [Schema.Types.ObjectId],
                ref: 'login',
                trim: true,
                index: true // Indexed for recipient-based queries
            },
            tAttachments: {
                type: [UserRequest._attachmentSchema]
            },
            aCount: {
                type: Number,
                trim: true,
                default: 0
            },
            bIsHalfDay: {
                type: Boolean,
                default: false,
                index: true // Indexed for half-day request filtering
            },
            dMultipleDates: {
                type: [UserRequest._dateRangeSchema]
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                trim: true,
                index: true // Indexed for faster lookups by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for UserRequest
     * @private
     */
    private static _model: Model<IUserRequest> = models?.UserRequest || model<IUserRequest>("userRequest", UserRequest._schema);

    /**
     * Get the Mongoose model for UserRequest
     * @returns {Model<IUserRequest>} The UserRequest model
     */
    public static get model(): Model<IUserRequest> {
        return UserRequest._model;
    }
}