import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SelectModule } from 'primeng/select';
import { InputTextModule } from 'primeng/inputtext';
import { FloatLabelModule } from 'primeng/floatlabel';
import { DatePickerModule } from 'primeng/datepicker';
import { CommonModule } from '@angular/common';
import { IColumn } from '../../models/Column';

@Component({
  selector: 'app-dynamic-search',
  standalone: true,
  imports: [
    SelectModule,
    ReactiveFormsModule,
    InputTextModule,
    FloatLabelModule,
    DatePickerModule,
    FormsModule,
    CommonModule,
  ],
  templateUrl: './dynamic-search.component.html',
})
export class DynamicSearchComponent implements OnInit {
  @Input() filters: IColumn[] = [];
  @Input() type: 'date' | 'text' = 'text';
  @Input() dateLabel: string = 'Date';

  @Output() selectedFilterChange = new EventEmitter<string>();
  @Output() searchedValueChange = new EventEmitter<string>();
  @Output() dateChange = new EventEmitter<Date>();

  selectedFilter = new FormControl('All');
  searchedValue = new FormControl();
  selectedDate: Date | null = null;

  constructor() {
    this.selectedFilter.valueChanges.subscribe(
      (value) => value != null && this.selectedFilterChange.emit(value)
    );
    this.searchedValue.valueChanges.subscribe(
      (value) => value != null && this.searchedValueChange.emit(value)
    );
  }
  ngOnInit(): void {
    this.filters.unshift({ field: 'All', header: 'All' });
    this.selectedFilter.setValue('All');
  }

  onDateSelected(event: Date | null) {
    this.selectedDate = event;
    if (!event) return;
    this.dateChange.emit(event);
  }
}
