<div class="grid min-h-screen w-full grid-cols-12 overflow-hidden">
  <div
    class="hidden md:flex w-full col-span-8 relative flex-col items-center justify-center h-screen overflow-hidden"
  >
    <h1 class="text-3xl font-semibold absolute top-28 w-fit right-36">
      AZAADI 2.0
    </h1>
    <img
      src="assets/images/login_background.png"
      alt="Illustration"
      class="object-cover h-screen w-full overflow-hidden"
    />
    <img
      src="assets/images/login_illustration.png"
      alt="Illustration"
      class="absolute bottom-0 right-0 w-1/2"
    />
  </div>

  <div
    class="w-full flex pt-28 pb-24 flex-col justify-center px-8 md:px-16 col-span-4 bg-gradient-to-b from-[#ffffff] to-[#ecffff]"
  >
    <div class="max-w-md w-full mx-auto flex flex-col justify-between h-full">
      <div class="text-center flex flex-col justify-center items-center gap-3">
        <h2 class="text-3xl mb-1">Hi , Welcome back!</h2>
        <p class="text-gray-500 mb-8">Let's login to your account.</p>
      </div>

      <form
        (ngSubmit)="onLogin()"
        [formGroup]="loginForm"
        class="space-y-12 px-12"
      >
        <p-floatlabel>
          <input
            pInputText
            id="email"
            type="email"
            formControlName="email"
            placeholder=" "
            class="peer w-full border-transparent text-gray-900 transition duration-200"
          />
          <label for="email">Email</label>
        </p-floatlabel>
        <p-floatlabel>
          <input
            pInputText
            id="password"
            type="password"
            formControlName="password"
            placeholder=" "
            class="peer w-full border-transparent text-gray-900 transition duration-200"
          />
          <label for="password">Password</label>
        </p-floatlabel>
        <div class="text-right mt-2">
          <a href="#" class="text-gray-400 text-xs hover:underline"
            >Forgot password?</a
          >
        </div>

        <div>
          <button
            pButton
            type="submit"
            label="Login"
            color="primary"
            class="w-full p-2 text-white"
          ></button>
        </div>
      </form>

      <div class="mt-10 text-center text-gray-400 text-xs">
        ©2025. All rights reserved
      </div>
    </div>
  </div>
  <div
    *ngIf="isLoading"
    class="h-dvh w-full flex items-center justify-center fixed backdrop-brightness-50"
  >
    <p-progress-spinner ariaLabel="loading" />
  </div>
</div>
