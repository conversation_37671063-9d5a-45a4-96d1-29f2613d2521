<p-card>
  <div class="flex">
    <div class="flex flex-col justify-around min-w-fit max-h-fit">
      <h1 class="text-[20px] font-semibold mb-4">
        {{ "dashboard.attendanceReport.title" | translate }}
      </h1>
      <div
        *ngFor="let item of attendanceStatus | keyvalue"
        class="flex items-center gap-2 py-1"
      >
        <span
          class="pi pi-circle-fill"
          [ngClass]="item.value | attendaceSymbolColor"
        ></span>
        <p>{{ item.value }}</p>
      </div>
    </div>
    <p-divider layout="vertical" />
    <div class="calendar rounded-md">
      <div class="calendar-header text-[var(--p-primary-500)]">
        <div (click)="datePopover.toggle($event)" class="cursor-pointer">
          {{ months[currentMonth] }} {{ currentYear }}
        </div>
        <div class="flex gap-2">
          <p-button
            variant="text"
            icon="pi pi-chevron-left"
            (click)="previousMonth()"
          />
          <p-button variant="text" (click)="goToTodayDate()">
            {{ "dashboard.attendanceReport.today" | translate }}
          </p-button>
          <p-button
            variant="text"
            icon="pi pi-chevron-right"
            (click)="nextMonth()"
          />
        </div>
      </div>

      <div class="calendar-grid place-items-center">
        <div class="calendar-day" *ngFor="let day of daysOfWeek">{{ day }}</div>
        <div
          class="calendar-date transition-all duration-1000 rounded-full"
          *ngFor="let date of calendarDates"
        >
          <p
            class="p-1 my-1 rounded-full h-8 aspect-square flex items-center justify-center"
            [ngClass]="getBackgroundColor(date) | attendanceReportClasses"
          >
            {{ date ? date.getDate() : "" }}
          </p>
        </div>
      </div>
    </div>
  </div>
</p-card>

<p-popover #datePopover>
  <app-date-select
    [currentDate]="getCurrentSelectedDate()"
    (selectedDateChange)="setSelectedDate($event)"
  />
</p-popover>
