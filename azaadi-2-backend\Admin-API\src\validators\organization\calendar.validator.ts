import { NextFunction, Request, Response } from "express";
import { body, ValidationChain, validationResult } from "express-validator";

/**
 * Class to handle calendar-related request validations
 */
export class CalendarValidator {
    /**
     * Get validation rules for calendar template creation/update
     */
    public static getCalendarTemplateValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Calendar template name validation (optional)
            body("sName")
                .optional()
                .trim()
                .isLength({ min: 2, max: 100 })
                .withMessage("Calendar template name must be between 2 and 100 characters"),

            // Calendar template tag validation (optional)
            body("sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Calendar template tag can only contain letters, numbers, hyphens and underscores")
                .isLength({ min: 2, max: 50 })
                .withMessage("Calendar template tag must be between 2 and 50 characters"),

            // Organization ID validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for event creation/update
     */
    public static getEventValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Event name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Event name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Event name must be between 2 and 100 characters"),

            // Event tag validation (optional)
            body("sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Event tag can only contain letters, numbers, hyphens and underscores"),

            // Event description validation (optional)
            body("sDescription")
                .optional()
                .trim()
                .isLength({ max: 1000 })
                .withMessage("Event description cannot exceed 1000 characters"),

            // Event URL validation (optional)
            body("sUrl")
                .optional()
                .trim()
                .isURL()
                .withMessage("Invalid URL format"),

            // Start date validation
            body("dStartDate")
                .notEmpty()
                .withMessage("Start date is required")
                .isISO8601()
                .withMessage("Invalid start date format")
                .toDate(),

            // End date validation (optional)
            body("dEndDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid end date format")
                .toDate()
                .custom((value, { req }) => {
                    if (value && new Date(value) <= new Date(req.body.dStartDate)) {
                        throw new Error('End date must be after start date');
                    }
                    return true;
                }),

            // Every year flag validation
            body("bEveryYear")
                .isBoolean()
                .withMessage("EveryYear must be a boolean"),

            // Is full day flag validation
            body("bIsFullDay")
                .isBoolean()
                .withMessage("IsFullDay must be a boolean"),

            // Calendar template ID validation
            body("tCalenderTemplate")
                .notEmpty()
                .withMessage("Calendar template ID is required")
                .isMongoId()
                .withMessage("Invalid calendar template ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for announcement creation/update
     */
    public static getAnnouncementValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Announcement name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Announcement name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Announcement name must be between 2 and 100 characters"),

            // Announcement description validation (optional)
            body("sDescription")
                .optional()
                .trim()
                .isLength({ max: 1000 })
                .withMessage("Announcement description cannot exceed 1000 characters"),

            // Announcement URL validation (optional)
            body("sUrl")
                .optional()
                .trim()
                .isURL()
                .withMessage("Invalid URL format"),

            // Start date validation (optional)
            body("dStartDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid start date format")
                .toDate(),

            // End date validation (optional)
            body("dEndDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid end date format")
                .toDate()
                .custom((value, { req }) => {
                    if (value && req.body.dStartDate && new Date(value) <= new Date(req.body.dStartDate)) {
                        throw new Error('End date must be after start date');
                    }
                    return true;
                }),

            // Organization ID validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
