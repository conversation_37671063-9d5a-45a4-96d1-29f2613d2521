import { ISuperUserActivityLog } from "../../interfaces/organization/manageLog.interface";
import { SuperUserActivityLog } from "../../models";
import Repository from "../repository";
import ISuperUserActivityLogRepository from "./abstracts/superUserActivityLogRepository.abstract";

export default class SuperUserActivityLogRepository extends Repository<ISuperUserActivityLog> implements ISuperUserActivityLogRepository {
    constructor() {
        super(SuperUserActivityLog.model);
    }
}