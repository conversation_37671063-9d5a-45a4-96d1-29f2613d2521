import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { IUser } from './user.interface';
import { IManageUserRequest } from '../organization/manageRequest.interface';
import { IOrganization } from '../organization/organization.interface';

export interface IAttachment extends IConcept {
    sFileName?: string;
    sFilePath?: string;
}

export interface IDateRange {
    dStartDate: Date;
    dEndDate: Date;
}

export interface IUserRequest extends IConcept {
    tSender: string | Types.ObjectId | IUser;
    dStartDate: Date;
    dEndDate: Date;
    tType?: string | Types.ObjectId | IManageUserRequest;
    tApprovedBy?: string | Types.ObjectId | IUser;
    eStatus?: string;
    sReason?: string;
    sMessage?: string;
    sReply?: string;
    tRecipients?: string[] | Types.ObjectId[] | IUser[];
    tAttachments?: IAttachment[];
    aCount?: number;
    bIsHalfDay?: boolean;
    dMultipleDates?: IDateRange[];
    tOrganization?: string | Types.ObjectId | IOrganization;
}