import {
  EmsFileType,
  EmsInterviewMode,
  EmsProcessType,
} from '../../enums/managements.enum';
import { EmsRole } from '../manageOrganization.interface';
import { EmsUser } from '../user.interface';
import { EmsRcmUser } from './rcm.user.interface';

interface EmsRcmGeneral {
  _id?: string;
  sName?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsRecruitment extends EmsRcmGeneral {
  tUser?: string | EmsRcmUser;
  tRcmProcess?: string | EmsRcmProcess;
  tCreatedBy?: string | EmsUser;
  bComplete?: boolean;
}

export interface EmsRcmProcess extends EmsRcmGeneral {
  sSteps?: string[] | EmsRcmSteps[];
}

export interface EmsRcmSteps extends EmsRcmGeneral {
  eProcessType?: EmsProcessType;
  tProcessData?:
  | string
  | EmsRcmExam
  | EmsRcmInterview
  | EmsRcmVerification
  | EmsRcmDocuments;
  bStatus?: boolean;
}

export interface EmsRcmInterview extends EmsRcmGeneral {
  sUrl?: string;
  tUser?: string | EmsRcmUser;
  tInterviewer?: string | EmsRole;
  dStartDateTime?: Date;
  dEndDateTime?: Date;
  aMarks?: number;
  aObtainedMarks?: number;
  bStatus?: boolean;
  sComments?: string;
}

export interface EmsRcmVerification extends EmsRcmGeneral {
  tReviewer?: string | EmsRole;
  tUser?: string | EmsRcmUser;
  bStatus?: boolean;
  sComments?: string;
}

export interface EmsRcmDocuments extends EmsRcmGeneral {
  eType?: EmsFileType[];
  bAllowMultiple?: boolean;
  tReviewer?: string | EmsRole;
  tUser?: string | EmsRcmUser;
  bStatus?: boolean;
  sComments?: string;
  tDocumentDetails?: EmsRcmDocumentDetails[];
}

export interface EmsRcmDocumentDetails {
  _id?: string;
  sName?: string;
  sSize?: string;
  sUrl?: string;
  eType?: EmsFileType;
  bStatus?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsRcmExam extends EmsRcmGeneral {
  dStartDateTime?: Date;
  dEndDateTime?: Date;
  aMarks?: number;
  aPassMark?: number;
  bStatus?: boolean;
  sComments?: string;
}

export interface EmsRcmInterview extends EmsRcmGeneral {
  sUrl?: string;
  tUser?: string | EmsRcmUser;
  tInterviewer?: string | EmsRole;
  dStartDateTime?: Date;
  dEndDateTime?: Date;
  aMarks?: number;
  aObtainedMarks?: number;
  bStatus?: boolean;
  sComments?: string;
  eInterviewMode?: EmsInterviewMode;
  sAddress?: string;
  sMapUrl?: string;
}
