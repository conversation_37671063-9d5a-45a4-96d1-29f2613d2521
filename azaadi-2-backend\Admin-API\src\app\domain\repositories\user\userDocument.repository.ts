import { IUserDocuments } from "../../interfaces/user/user-document.interface";
import { UserDocument } from "../../models";
import Repository from "../repository";
import IUserDocumentRepository from "./abstract/userDocumentRepository.abstract";

export default class UserDocumentRepository extends Repository<IUserDocuments> implements IUserDocumentRepository {
    constructor() {
        super(UserDocument.model);
    }
}