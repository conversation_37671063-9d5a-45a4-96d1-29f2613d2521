import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain, validationResult } from "express-validator";
import { EMSFileType } from "../../utils/meta/enum.utils";

/**
 * Class to handle document management-related request validations
 */
export class ManageDocumentValidator {
    /**
     * Get validation rules for document management creation/update
     */
    public static getManageDocumentValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Tag validation (optional)
            body("sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Tag can only contain letters, numbers, hyphens and underscores")
                .isLength({ min: 2, max: 50 })
                .withMessage("Tag must be between 2 and 50 characters"),

            // File types validation
            body("eType")
                .isArray()
                .withMessage("File types must be an array")
                .notEmpty()
                .withMessage("At least one file type is required")
                .custom((value) => {
                    if (!Array.isArray(value)) {
                        throw new Error("File types must be an array");
                    }
                    
                    const validTypes = Object.values(EMSFileType);
                    const allValid = value.every(type => 
                        validTypes.includes(Number(type))
                    );
                    
                    if (!allValid) {
                        throw new Error("Invalid file type. Must be one of: PDF, JPG, DOC, EXCEL, XML");
                    }
                    return true;
                }),

            // Allow multiple files validation
            body("bAllowMultiple")
                .isBoolean()
                .withMessage("AllowMultiple must be a boolean")
                .optional()
                .default(false),

            // Organization validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for bulk document management operations
     */
    public static getBulkManageDocumentValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Validate array of document entries
            body()
                .isArray()
                .withMessage("Request body must be an array of document entries"),
            
            // Name validation for each entry
            body("*.sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Tag validation for each entry (optional)
            body("*.sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Tag can only contain letters, numbers, hyphens and underscores"),

            // File types validation for each entry
            body("*.eType")
                .isArray()
                .withMessage("File types must be an array")
                .notEmpty()
                .withMessage("At least one file type is required")
                .custom((value) => {
                    if (!Array.isArray(value)) {
                        throw new Error("File types must be an array");
                    }
                    
                    const validTypes = Object.values(EMSFileType);
                    const allValid = value.every(type => 
                        validTypes.includes(Number(type))
                    );
                    
                    if (!allValid) {
                        throw new Error("Invalid file type. Must be one of: PDF, JPG, DOC, EXCEL, XML");
                    }
                    return true;
                }),

            // Allow multiple files validation for each entry
            body("*.bAllowMultiple")
                .isBoolean()
                .withMessage("AllowMultiple must be a boolean")
                .optional()
                .default(false),

            // Organization validation for each entry
            body("*.tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
