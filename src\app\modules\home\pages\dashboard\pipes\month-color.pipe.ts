import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'monthColor',
})
export class MonthColorPipe implements PipeTransform {
  transform(date: Date): string {
    const month = date.getMonth();

    const monthColors = [
      '#E74C3C', // January - Bright Red
      '#FF7F50', // February - Coral
      '#FFD700', // March - Gold
      '#2ECC71', // April - Emerald Green
      '#9B59B6', // May - Amethyst
      '#1E90FF', // June - Dodger Blue
      '#FF1493', // July - Deep Pink
      '#20B2AA', // August - Light Sea Green
      '#FF8C00', // September - Dark Orange
      '#8B0000', // October - Dark Red
      '#4B0082', // November - Indigo
      '#4682B4'  // December - Steel Blue
    ];

    return monthColors[month] || '#000000';
  }
}
