<p-card>
  <div class="flex justify-between mb-6">
    <h1 class="text-[20px] font-semibold">
      {{ "dashboard.holiday.title" | translate }}
    </h1>
    <p-button
      (onClick)="openHolidayDialog()"
      [variant]="'text'"
      [label]="'dashboard.holiday.viewAll' | translate"
    />
  </div>
  <div *ngFor="let holiday of holidays" class="flex justify-between mb-4">
    <div class="flex gap-6">
      <img
        [src]="holiday.image"
        alt=""
        class="h-12 w-12 rounded-full object-cover"
      />
      <div>
        <h2 class="text-[17px] font-semibold">{{ holiday.title }}</h2>
        <p
          [pTooltip]="holiday.description"
          tooltipPosition="bottom"
          class="text-[#979797] overflow-hidden text-ellipsis whitespace-nowrap max-w-[400px]"
        >
          {{ holiday.description }}
        </p>
      </div>
    </div>
    <div class="text-center">
      <p class="font-600 text-[var(--p-surface-400)] mb-1">
        {{ holiday.date | date : "dd.MM.YYYY" }}
      </p>
      <p
        class="border rounded-[30px] p-1 text-[10px] border-[var(--p-primary-500)] text-[var(--p-primary-500)]"
      >
        {{ holiday.date | date : "EEEE" }}
      </p>
    </div>
  </div>
</p-card>
