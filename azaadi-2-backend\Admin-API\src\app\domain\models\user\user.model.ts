import { model, Model, models, Schema } from "mongoose";
import { IUser } from "../../interfaces/user/user.interface";
import { PasswordMiddleware } from "../../../../middlewares/password.middleware";

/**
 * User model class that defines the schema and provides access to the User collection.
 * 
 * @class User
 * @description Handles the schema definition and model creation for Users
 */
export default class User {
    /**
     * Mongoose schema definition for User
     * @private
     */
    private static readonly _schema = new Schema<IUser>(
        {
            tIdEmployee: {
                type: Schema.Types.ObjectId,
                ref: "employeeCode",
                trim: true,
                index: true // Index for employee lookups
            },
            sPassword: {
                type: String,
                required: [true, "Password must be required!"],
                trim: true
            },
            sEmail: {
                type: String,
                trim: true,
                unique: [true, "Email id already exists"],
                index: true // Index for email lookups
            },
            aPhoneNumber: {
                type: Number,
                trim: true,
                index: true // Index for phone number lookups
            },
            sWorkingType: {
                type: String,
                trim: true,
                index: true // Index for working type filtering
            },
            sProfileUrl: {
                type: String,
                trim: true
            },
            tBranches: {
                type: [Schema.Types.ObjectId],
                ref: "branch",
                trim: true,
                index: true // Index for branch-based queries
            },
            tOrganizations: {
                type: [Schema.Types.ObjectId],
                ref: "organization",
                trim: true,
                index: true // Index for organization filtering
            },
            tRole: {
                type: Schema.Types.ObjectId,
                ref: "role",
                trim: true,
                index: true // Index for role-based queries
            },
            tDepartment: {
                type: Schema.Types.ObjectId,
                ref: "department",
                trim: true,
                index: true // Index for department filtering
            },
            tDesignation: {
                type: Schema.Types.ObjectId,
                ref: "designation",
                trim: true,
                index: true // Index for designation filtering
            },
            tUserDetails: {
                type: Schema.Types.ObjectId,
                ref: "userdetails",
                trim: true
            },
            bIsActive: {
                type: Boolean,
                default: true,
                index: true // Index for active/inactive filtering
            },
            bCanLogin: {
                type: Boolean,
                default: true,
                index: true // Index for login status filtering
            },
            bOnlyOfficePunch: {
                type: Boolean,
                default: false
            },
            tShift: {
                type: [Schema.Types.ObjectId],
                ref: "shift",
                index: true // Index for shift-based queries
            },
            bIsApplicableAdvance: {
                type: Boolean,
                default: false,
                index: true // Index for advance applicability filtering
            },
            dInactiveDate: {
                type: Date,
                sparse: true, // Only index documents where this field exists
                index: true // Index for date-based queries
            },
            bIsResigned: {
                type: Boolean,
                default: false,
                index: true // Index for resignation status filtering
            },
            bIsCreatedBySuperAdmin: {
                type: Boolean,
                default: false
            },
            bIsPermanentWFH: {
                type: Boolean,
                default: false,
                index: true // Index for WFH status filtering
            }
        },
        {
            timestamps: true,
            versionKey: false
        }
    );

    static {
        // Compound indexes for optimizing common query patterns
        
        // Index for organization-role based queries
        // This helps in scenarios like:
        // 1. Finding all users of a specific role within an organization
        // 2. Role-based access control within organization context
        // 3. Organization-specific role management
        // Example queries:
        // await User.find({ tOrganizations: orgId, tRole: roleId }) // Find users with specific role in org
        // await User.find({ tOrganizations: orgId, tRole: { $in: roleIds }}) // Find users with multiple roles
        // await User.aggregate([
        //     { $match: { tOrganizations: orgId }},
        //     { $group: { _id: "$tRole", count: { $sum: 1 }}} // Count users by role in org
        // ])
        User._schema.index({ tOrganizations: 1, tRole: 1 });

        // Index for organization-department based queries
        // This optimizes queries for:
        // 1. Finding all users in a specific department within an organization
        // 2. Department-wise user management
        // 3. Organization hierarchy traversal
        // Example queries:
        // await User.find({ tOrganizations: orgId, tDepartment: deptId }) // Users in department
        // await User.find({ 
        //     tOrganizations: orgId, 
        //     tDepartment: deptId,
        //     bIsActive: true 
        // }) // Active users in department
        // await User.aggregate([
        //     { $match: { tOrganizations: orgId }},
        //     { $group: { _id: "$tDepartment", userCount: { $sum: 1 }}} // Users per department
        // ])
        User._schema.index({ tOrganizations: 1, tDepartment: 1 });

        // Text index for search functionality
        // Enables efficient text-based searches on:
        // 1. Email addresses for user lookup
        // 2. Phone numbers for contact information search
        // 3. Full-text search capabilities across both fields
        // Example queries:
        // await User.find({ $text: { $search: "<EMAIL>" }}) // Search by email
        // await User.find({ $text: { $search: "1234567890" }}) // Search by phone
        // await User.find({ 
        //     $text: { $search: searchTerm },
        //     score: { $meta: "textScore" } 
        // }).sort({ score: { $meta: "textScore" }}) // Full text search with ranking
        User._schema.index({ sEmail: 'text', aPhoneNumber: 'text' }, { name: 'text_search' });
        
        // Add password hashing middleware
        new PasswordMiddleware().addHashingMiddleware(User._schema);
    }

    /**
     * Mongoose model for User
     * @private
     */
    private static readonly _model: Model<IUser> = models?.User || model<IUser>("login", User._schema);

    /**
     * Get the Mongoose model for User
     * @returns {Model<IUser>} The User model
     */
    public static get model(): Model<IUser> {
        return User._model;
    }
}