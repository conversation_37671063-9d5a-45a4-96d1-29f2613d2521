<p-card>
  <ng-container *ngIf="!isLoading; else elseBlock">
    <div class="flex gap-4 justify-between h-[200px]">
      <div>
        <h1 class="text-[17px] font-semibold h-[20%]">
          {{ "dashboard.timeTracker.title" | translate }}
        </h1>
        <div class="h-[80%] flex items-center justify-center">
          <app-circular-progress
            [label]="timeString"
            subLabel="{{ 'dashboard.timeTracker.remainingTime' | translate }}"
            [size]="130"
            [progress]="progressDeg"
          />
        </div>
      </div>
      <div class="flex flex-col justify-around">
        <p-tag
          [value]="'Mode: ' + workingMode"
          styleClass="mb-4"
          rounded="true"
        />
        <div class="mb-2">
          <p class="text-[var(--p-surface-400)] text-[12px]">
            {{ "dashboard.timeTracker.totalHours" | translate }}
          </p>
          <p class="text-[20px] font-semibold line">
            {{ totalHours }}{{ "common.hour" | translate }}
          </p>
        </div>
        <div class="mb-2">
          <p class="text-[var(--p-surface-400)] text-[12px]">
            {{ "dashboard.timeTracker.shift" | translate }}
          </p>
          <p class="text-[20px] font-semibold">{{ shift }}</p>
        </div>
        <div class="mb-2">
          <p class="text-[var(--p-surface-400)] text-[12px]">
            {{ "dashboard.timeTracker.totalTimeWorked" | translate }}
          </p>
          <p class="text-[20px] font-semibold">
            {{ formatElapsedTime() }}
          </p>
        </div>
      </div>
    </div>
  </ng-container>

  <ng-template #elseBlock>
    <div class="flex justify-center items-center h-[200px]">
      <p-progress-spinner ariaLabel="loading" />
    </div>
  </ng-template>
</p-card>
