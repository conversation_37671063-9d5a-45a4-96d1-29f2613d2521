import {
  EmsOrganization,
  EmsRole,
} from './organization/manageOrganization.interface';
import { EmsUser } from './users/user.interface';

export interface ICommon {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ICommonName extends ICommon {
  sName: string;
}

export interface ICommonSID extends Partial<Pick<ICommonName, 'sName'>> {
  sId?: string;
}
export interface ICommonTag extends ICommonName {
  sTag?: string;
}

export interface ICommonStringType {
  sType: string;
}

export interface ICommonTileValue extends ICommonStringType {
  sTitle: string;
  sValue: any;
}

export interface ICommonDescription {
  sDescription?: string;
}
export interface ICommonOrg {
  tOrganization: string | EmsOrganization;
}

export interface ICommonRole {
  tRole: EmsRole | string;
}

export interface ICommonAccess {
  bIsActive?: Boolean;
  bCanLogin?: Boolean;
  bIsPublish?: boolean;
}

export interface ICommonIdUser {
  tIdUser: string | EmsUser;
}

export interface ICommonStartEndDate {
  dStartDate?: Date;
  dEndDate?: Date;
}

export interface ICommonArrStartEndDate {
  dStartDate?: Date[];
  dEndDate?: Date[];
}

export interface ICommonOrganization {
  tOrganization?: string | EmsOrganization;
}

export interface ICommonAttachment {
  sFilename?: string;
  sPath?: string;
}

export interface ICommonApprover {
  tApprovedBy: EmsUser | string;
}

export interface ICommonComment extends ICommonApprover {
  sReason: string;
  sMessage: string;
}

export interface ICommonProfile {
  sEmail: string;
  sPassword: string;
  aPhoneNumber?: number;
  sProfileUrl?: string;
}

export interface ICommonFile {
  sFilename?: string;
  sPath?: string;
}

export interface ICommonMonthCount {
  aMonth: number;
  aYear: number;
  aAmount: number;
}

// abstraction common
export interface ICommonDetails
  extends ICommonIdUserWithName,
    ICommonAccess,
    ICommonOrganization {}

export interface ICommonUser extends ICommonProfile, ICommon, ICommonAccess {}

export interface ICommonUserLog
  extends ICommonWithIdUser,
    ICommonOrganization {}

export interface ICommonWithIdUser extends ICommon, ICommonIdUser {}

export interface ICommonIdUserWithName extends ICommonName, ICommonIdUser {}

export interface ICommonWithSEDate extends ICommon, ICommonStartEndDate {}

export interface ICommonIdWithArrSEDate
  extends ICommonWithIdUser,
    ICommonArrStartEndDate {}

export interface ICommonRequest
  extends ICommonWithSEDate,
    ICommonOrganization,
    ICommonComment {}

export interface ICommonLeave
  extends ICommonComment,
    ICommonWithIdUser,
    ICommonStartEndDate {}

export interface ICommonSupperAdmin
  extends ICommonAccess,
    ICommonName,
    ICommonProfile {}

export interface ICommonSalary
  extends ICommon,
    ICommonIdUser,
    ICommonStartEndDate {
  aTotalAmount: number;
}
