import { model, Model, models, Schema } from "mongoose";
import { IEmployeeCode } from "../../interfaces/user/employee-code.interface";

/**
 * EmployeeCode model class that defines the schema and provides access to the EmployeeCode collection.
 * 
 * @class EmployeeCode
 * @description Handles the schema definition and model creation for Employee Codes
 */
export default class EmployeeCode {
    /**
     * Mongoose schema definition for EmployeeCode
     * @private
     */
    private static readonly _schema: Schema<IEmployeeCode> = new Schema<IEmployeeCode>(
        {
            tRole: {
                type: Schema.Types.ObjectId,
                ref: "role",
                required: [true, "Role is missing"],
                trim: true,
                index: true // Index for role-based queries
            },
            aPunchId: {
                type: Number,
                trim: true,
                unique: [true, "PunchId is already exist"],
                sparse: true // Allow multiple null values for optional field
            },
            sCode: {
                type: String,
                trim: true,
                unique: [true, "Duplicate code found!"],
                sparse: true // Allow multiple null values for optional field
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for EmployeeCode
     * @private
     */
    private static _model: Model<IEmployeeCode> = models?.employeeCode || model<IEmployeeCode>("employeeCode", EmployeeCode._schema);

    /**
     * Get the Mongoose model for EmployeeCode
     * @returns {Model<IEmployeeCode>} The EmployeeCode model
     */
    public static get model(): Model<IEmployeeCode> {
        return EmployeeCode._model;
    }
}