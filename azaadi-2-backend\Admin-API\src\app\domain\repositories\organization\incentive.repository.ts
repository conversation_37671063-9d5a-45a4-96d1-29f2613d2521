import { IIncentive } from "../../interfaces/organization/incentive.interface";
import { Incentive } from "../../models";
import Repository from "../repository";
import IIncentiveRepository from "./abstracts/incentiveRepository.abstract";

export default class IncentiveRepository extends Repository<IIncentive> implements IIncentiveRepository {
    constructor() {
        super(Incentive.model);
    }
}