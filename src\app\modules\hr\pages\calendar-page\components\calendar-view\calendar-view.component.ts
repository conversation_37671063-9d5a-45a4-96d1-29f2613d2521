import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

@Component({
  selector: 'app-calendar-view',
  imports: [CommonModule],
  templateUrl: './calendar-view.component.html',
})
export class CalendarViewComponent {
  weekDays = ['MON', 'TUES', 'WED', 'THURS', 'FRI', 'SAT', 'SUN'];

  weekData = [
    { date: 7 },
    { date: 8 },
    { date: 9 },
    { date: 10, isToday: true },
    { date: 11, isOutOfOffice: true, label: 'Out of Office' },
    { date: 12, isLeave: true, label: 'WFH - OFF' },
    { date: 13 }
  ];
}
