import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { IOrganization } from './organization.interface';

export interface ICalendarTemplate extends IConcept {
    sName?: string;
    sTag?: string;
    tOrganization: string | Types.ObjectId | IOrganization;
}

export interface IEvent extends IConcept {
    sName: string;
    sTag?: string;
    sDescription?: string;
    sUrl?: string;
    dStartDate: Date;
    dEndDate?: Date;
    bEveryYear: boolean;
    bIsFullDay: boolean;
    tCalenderTemplate: string | Types.ObjectId | ICalendarTemplate;
}

export interface IAnnouncement extends IConcept {
    sName: string;
    sDescription?: string;
    sUrl?: string;
    dStartDate?: Date;
    dEndDate?: Date;
    tOrganization: string | Types.ObjectId | IOrganization;
}