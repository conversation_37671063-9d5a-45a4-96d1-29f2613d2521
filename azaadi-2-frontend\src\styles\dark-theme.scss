// This file was generated by running 'ng generate @angular/material:m3-theme'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '@angular/material' as mat;
// Note: Color palettes are generated from primary: #0e1012
$_palettes: (
  primary: (
    0: #000000,
    10: #161c21,
    20: #2b3137,
    25: #363c42,
    30: #41474d,
    35: #4d5359,
    40: #595f65,
    50: #72787e,
    60: #8b9198,
    70: #a6acb3,
    80: #c1c7ce,
    90: #dde3ea,
    95: #ecf1f9,
    98: #f6f9ff,
    99: #fcfcff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #0c1d29,
    20: #22323f,
    25: #2d3d4a,
    30: #384956,
    35: #445462,
    40: #50606f,
    50: #687988,
    60: #8293a2,
    70: #9cadbd,
    80: #b7c9d9,
    90: #d3e5f6,
    95: #e6f2ff,
    98: #f6f9ff,
    99: #fcfcff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #201634,
    20: #362b4a,
    25: #413656,
    30: #4d4162,
    35: #594d6e,
    40: #65587b,
    50: #7e7195,
    60: #998ab0,
    70: #b4a5cb,
    80: #cfc0e8,
    90: #ebdcff,
    95: #f7edff,
    98: #fef7ff,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1a1c1e,
    20: #2e3133,
    25: #3a3c3e,
    30: #454749,
    35: #515255,
    40: #5d5e61,
    50: #76777a,
    60: #8f9193,
    70: #aaabae,
    80: #c6c6c9,
    90: #e2e2e5,
    95: #f0f0f3,
    98: #f9f9fc,
    99: #fcfcff,
    100: #ffffff,
    4: #0c0e11,
    6: #111416,
    12: #1e2022,
    17: #282a2d,
    22: #333537,
    24: #37393c,
    87: #d9dadd,
    92: #e8e8eb,
    94: #edeef1,
    96: #f3f3f6,
  ),
  neutral-variant: (
    0: #000000,
    10: #161c21,
    20: #2b3137,
    25: #363c42,
    30: #41474d,
    35: #4d5359,
    40: #595f65,
    50: #72787e,
    60: #8b9198,
    70: #a6acb3,
    80: #c1c7ce,
    90: #dde3ea,
    95: #ecf1f9,
    98: #f6f9ff,
    99: #fcfcff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);
$_primary: map.merge(map.get($_palettes, primary), $_rest);
$_tertiary: map.merge(map.get($_palettes, tertiary), $_rest);
$dark-theme: mat.define-theme((
  color: (
    theme-type: dark,
    primary: $_primary,
    tertiary: $_tertiary,
  ),
));

.dark {
  @include mat.all-component-themes($dark-theme);
    // colors
    --color-primary: #0f0f0f;
    --text-color-primary:#ffffff;
    --color-secondary:#17191b;
    --text-color-secondary:#f6faff;
  //background
  // --mat-sidenav-content-background-color: #31363F;
  --mat-toolbar-container-background-color:var( --color-primary);
  --mat-toolbar-container-text-color:var(--text-color-primary);
  --mat-sidenav-container-background-color: #1f2022;
  --mat-expansion-container-background-color: #1f2022;
  --mat-sidenav-container-width:250px;
  --mat-expansion-container-shape:0;
  --mdc-list-list-item-hover-leading-icon-color:#f5f5f5;
  --mat-divider-color: #545457;
}