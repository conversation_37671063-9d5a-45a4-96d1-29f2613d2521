import { EmsDeliveryType } from "../../enums/chat.enum";
import { ICommonName, ICommonProfile, ICommon, ICommonTileValue } from "../common.interface";

export interface EmsChatUser extends ICommonName ,Pick<ICommonProfile,'sProfileUrl'>{
    tEmsUserId?: string;
    bIsAdmin: boolean;
}
export interface EmsChatGroup extends ICommon{
    tIdChatUsers: EmsChatUser[] | string[];
    aCount?: number;
}
export interface EmsConversion extends ICommon, Pick<ICommonTileValue,'sTitle'> {
    tIdGroup?: string | EmsChatGroup;
    bIsSolved: boolean;
}
export interface EmsChat extends ICommon{
    sMessage: string;
    tSenders: EmsChatUser | string;
    tSeen: EmsSeenBy[] | string[];
    tIdConversations: EmsConversion | string
}
export interface EmsSeenBy extends ICommon{
    tChatUserId: string | EmsChatUser;
    eDeliveryType: EmsDeliveryType;
}