<mat-menu #menu="matMenu">
  <mat-list>
    @if (showProfile) {
    <mat-list-item >
      <div class="div-grid">
        <img
          class="menu-image"
          src="https://material.angular.io/assets/img/examples/shiba1.jpg"
          alt=""
          srcset=""
        />
        <div style="display: block; padding-left: 35px">
          <div matListItemTitle>Rizwan Ha<PERSON></div>
          <div matListItemLine>PMD012</div>
          <div matListItemLine class="link" (click)="onMenuOptionClick('profile')">My Profile</div>
        </div>
      </div>
    </mat-list-item>
    } @if (showCommon) {
    <mat-divider></mat-divider>
    <mat-list-item>
      <mat-icon matListItemIcon>calendar_month</mat-icon>
      <div matListItemTitle>Calender</div>
    </mat-list-item>
    <mat-list-item>
      <mat-icon matListItemIcon>swap_horiz</mat-icon>
      <div matListItemTitle>Switch Organization</div>
      <div matListItemLine>BASSETTI</div>
    </mat-list-item>
    <mat-list-item>
      <mat-icon matListItemIcon>exit_to_app</mat-icon>
      <div matListItemTitle>Sign Out</div>
    </mat-list-item>
    } @if (showAppearance) {
    <mat-divider></mat-divider>
    <mat-list-item>
      <mat-icon matListItemIcon>translate</mat-icon>
      <div matListItemTitle>Language</div>
      <div matListItemLine>English</div>
    </mat-list-item>
    <mat-list-item (click)="$event.stopPropagation()">
      <mat-icon matListItemIcon>{{
        theme == "light" ? "light_mode" : "dark_mode"
      }}</mat-icon>
      <div matListItemTitle (click)="toggleTheme()">
        Appearance
        <mat-slide-toggle
          #darkModeSwitch
          [checked]="theme == 'light' ? false : true"
        ></mat-slide-toggle>
      </div>
      <div matListItemLine>{{ theme }}</div>
    </mat-list-item>

    } @if (showSetting) {
    <mat-divider></mat-divider>
    <mat-list-item>
      <mat-icon matListItemIcon>settings</mat-icon>
      <div matListItemTitle>Setting</div>
    </mat-list-item>

    } @if (showHelp) {
    <mat-divider></mat-divider>
    <mat-list-item>
      <mat-icon matListItemIcon>help</mat-icon>
      <div matListItemTitle>Help</div>
    </mat-list-item>
    <mat-list-item>
      <mat-icon matListItemIcon>support</mat-icon>
      <div matListItemTitle>IT Support</div>
    </mat-list-item>
    }
  </mat-list>
</mat-menu>
