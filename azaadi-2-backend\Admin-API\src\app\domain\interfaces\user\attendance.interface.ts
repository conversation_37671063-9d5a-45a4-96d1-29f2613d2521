import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { EMSAttendanceType, EMSAttendanceStatus } from '../../../../utils/meta/enum.utils';
import { IShift } from '../organization/shift.interface';
import { IUser } from './user.interface';

export interface ILocationTracker extends IConcept {
    sLongitude?: string;
    sLatitude?: string;
    sTimestamp?: string;
    sLocation?: string;
}

export interface IUserAttendance extends IConcept {
    tIdUser: string | Types.ObjectId | IUser;
    dStartDate: Date[];
    dEndDate: Date[];
    eAttendanceType?: EMSAttendanceType;
    eEndAttendanceType?: EMSAttendanceType;
    tShift?: string | Types.ObjectId | IShift;
    eStatus?: EMSAttendanceStatus;
    aLateDuration?: number;
    sWorkingHours?: string;
    tActivityTracker?: ILocationTracker[];
}