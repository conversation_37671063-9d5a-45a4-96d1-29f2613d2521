import { Types } from "mongoose";
import { IRoleInfoDTO } from "./role.dto";

export interface IDepartmentInfoDTO {
    _id: string | Types.ObjectId;
    sName: string;
    sTag: string;
    tDepartmentHead?: IRoleInfoDTO;
}

export interface IDesignationInfoDTO {
    _id: string | Types.ObjectId;
    sName: string;
    sTag?: string;
}

export interface IEmployeeCodeInfoDTO {
    _id: string | Types.ObjectId;
    sCode: string;
    aPunchId: number;
}