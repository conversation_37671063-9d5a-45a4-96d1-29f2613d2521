import { model, Model, models, Schema } from "mongoose";
import { EMSOrganizationSettingLabel } from "../../../../utils/meta/enum.utils";
import { IOrganizationSetting } from "../../interfaces/organization/organization.interface";

/**
 * OrganizationSettings model class that defines the schema and provides access to the OrganizationSettings collection.
 * 
 * @class OrganizationSettings
 * @description Handles the schema definition and model creation for Organization Settings
 */
export default class OrganizationSettings {
    /**
     * Mongoose schema definition for OrganizationSettings
     * @private
     */
    private static _schema = new Schema<IOrganizationSetting>(
        {
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                required: [true, "Organization ID is required"],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            },
            sOrgTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by organization tag
            },
            sTitle: {
                type: String,
                enum: EMSOrganizationSettingLabel,
                required: [true, "Setting title is required"],
                index: true // Indexed for quick lookups of specific setting types
            },
            sValue: {
                type: Schema.Types.Mixed,
                trim: true,
                required: [true, "Setting value is required"]
            },
            sType: {
                type: String,
                trim: true,
                index: true // Indexed for filtering by setting type
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking since we're not using optimistic concurrency control
        }
    );

    /**
     * Mongoose model for OrganizationSettings
     * @private
     */
    private static _model: Model<IOrganizationSetting> = models?.OrganizationSettings || model<IOrganizationSetting>("orgSettings", OrganizationSettings._schema);

    /**
     * Get the Mongoose model for OrganizationSettings
     * @returns {Model<IOrganizationSetting>} The OrganizationSettings model
     */
    public static get model(): Model<IOrganizationSetting> {
        return OrganizationSettings._model;
    }
}