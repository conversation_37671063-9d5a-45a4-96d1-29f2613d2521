import { CommonModule } from '@angular/common';
import { Component, EventEmitter, linkedSignal, Output, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputTextModule } from 'primeng/inputtext';
import { TICKETS_DUMMY_DATA } from 'src/assets/dummy_data/ticket';
import { ITicket } from '../../models/ticket';
import { TicketCardComponent } from "../ticket-card/ticket-card.component";

@Component({
  selector: 'app-ticket-left-panel',
  imports: [TicketCardComponent, CommonModule, FormsModule, InputTextModule, InputGroupModule, InputGroupAddonModule],
  templateUrl: './ticket-left-panel.component.html',
  styleUrl: './ticket-left-panel.component.scss'
})
export class TicketLeftPanelComponent {
  @Output() selectedTicketId = new EventEmitter<number>();
  searchTicket : string = '';
  tickets = signal<ITicket[]>(TICKETS_DUMMY_DATA);
  selectedTicket = linkedSignal(() => this.tickets()[0].id);
  navigateToTicket(ticketId: number) {
    this.selectedTicket.set(ticketId);
    this.selectedTicketId.emit(ticketId);
  }
}
