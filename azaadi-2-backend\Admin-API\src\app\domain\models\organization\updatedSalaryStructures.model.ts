import { model, Model, models, Schema, Types } from "mongoose";
import { IUpdatedSalaryStructure } from "../../interfaces/organization/salaryStructure.interface";
import { EMSUpdatedSalaryStructureCategory } from "../../../../utils/meta/enum.utils";

/**
 * UpdatedSalaryStructures model class that defines the schema and provides access to the UpdatedSalaryStructures collection.
 * 
 * @class UpdatedSalaryStructures
 * @description Handles the schema definition and model creation for Updated Salary Structures
 */
export default class UpdatedSalaryStructures {
    /**
     * Mongoose schema definition for UpdatedSalaryStructures
     * @private
     */
    private static _schema = new Schema<IUpdatedSalaryStructure>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                required: [true, "User ID is required"],
                trim: true,
                ref: "login",
                index: true // Indexed for faster lookups by user
            },
            tSalaryStructure: {
                type: Schema.Types.Mixed
            },
            eType: {
                type: String,
                enum: EMSUpdatedSalaryStructureCategory,
                required: [true, "Salary structure type is required"],
                index: true // Indexed for filtering by salary structure type
            },
            bHavingAdvancePayment: {
                type: Boolean,
                default: false,
                index: true // Indexed for filtering by advance payment status
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                required: [true, "Organization is required"],
                trim: true,
                index: true // Indexed for faster lookups by organization
            },
            isActivePF: {
                type: Boolean,
                default: true,
                index: true // Indexed for filtering by PF status
            },
            isActiveESI: {
                type: Boolean,
                default: true,
                index: true // Indexed for filtering by ESI status
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for UpdatedSalaryStructures
     * @private
     */
    private static _model: Model<IUpdatedSalaryStructure> = models?.UpdatedSalaryStructures || model<IUpdatedSalaryStructure>("updatedSalaryStructure", UpdatedSalaryStructures._schema);

    /**
     * Get the Mongoose model for UpdatedSalaryStructures
     * @returns {Model<IUpdatedSalaryStructure>} The UpdatedSalaryStructures model
     */
    public static get model(): Model<IUpdatedSalaryStructure> {
        return UpdatedSalaryStructures._model;
    }
}