import { Schema, model, Model, models } from 'mongoose';
import { IUserLog, ILogData } from '../../interfaces/user/user-log.interface';
import { EmsLogOperationType } from '../../../../utils/meta/enum.utils';

/**
 * UserLog model class that defines the schema and provides access to the UserLog collection.
 * 
 * @class UserLog
 * @description Handles the schema definition and model creation for User Logs
 */
export default class UserLog {
    /**
     * Mongoose schema definition for log data
     * @private
     */
    private static readonly _logDataSchema = new Schema<ILogData>(
        {
            sMetaKey: {
                type: String,
                required: [true, "Meta key is required"],
                trim: true,
                index: true // Indexed for searching by meta key
            },
            sMetaValue: {
                type: Schema.Types.Mixed,
                required: [true, "Meta value is required"]
            }
        },
        {
            _id: true // Maintain separate IDs for log data entries
        }
    );

    /**
     * Mongoose schema definition for UserLog
     * @private
     */
    private static readonly _schema = new Schema<IUserLog>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                ref: 'login',
                required: [true, "User ID is required"],
                trim: true,
                index: true // Indexed for faster lookups by user
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                trim: true,
                index: true // Indexed for faster lookups by organization
            },
            tPreviousData: [UserLog._logDataSchema],
            tCurrentData: [UserLog._logDataSchema],
            sOperationType: {
                type: String,
                enum: EmsLogOperationType,
                required: [true, "Operation type is required"],
                index: true // Indexed for filtering by operation type
            },
            dOperationDate: {
                type: Date,
                required: [true, "Operation date is required"],
                index: true // Indexed for date-based queries
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    static {
        // Compound index for user-organization queries
        // Optimizes queries like:
        // 1. Find all logs for a user in an organization
        // 2. Organization-specific user activity tracking
        UserLog._schema.index({ tIdUser: 1, tOrganization: 1 });

        // Compound index for date-operation based queries
        // Optimizes queries like:
        // 1. Find all operations of a specific type in a date range
        // 2. Activity timeline filtering
        UserLog._schema.index({ dOperationDate: 1, sOperationType: 1 });

        // Text index for searching through meta keys and values
        // Enables natural language search across log data
        UserLog._schema.index(
            { 
                'tPreviousData.sMetaKey': 'text',
                'tCurrentData.sMetaKey': 'text'
            },
            {
                weights: {
                    'tCurrentData.sMetaKey': 2, // Current data has higher priority
                    'tPreviousData.sMetaKey': 1
                },
                name: 'log_text_index'
            }
        );
    }

    /**
     * Mongoose model for UserLog
     * @private
     */
    private static _model: Model<IUserLog> = models?.userLog || model<IUserLog>('userLog', UserLog._schema);

    /**
     * Get the Mongoose model for UserLog
     * @returns {Model<IUserLog>} The UserLog model
     */
    public static get model(): Model<IUserLog> {
        return UserLog._model;
    }
}