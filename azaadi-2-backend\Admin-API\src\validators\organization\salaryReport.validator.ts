import { NextFunction, Request, Response } from "express";
import { body, ValidationChain, validationResult } from "express-validator";

/**
 * Class to handle salary report-related request validations
 */
export class SalaryReportValidator {
    /**
     * Get validation rules for different salary sheet creation/update
     */
    public static getDifferentSalarySheetValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Month validation
            body("aMonth")
                .notEmpty()
                .withMessage("Month is required")
                .isInt({ min: 1, max: 12 })
                .withMessage("Month must be between 1 and 12"),

            // Year validation
            body("aYear")
                .notEmpty()
                .withMessage("Year is required")
                .isInt({ min: 1900, max: 9999 })
                .withMessage("Invalid year"),

            // Total amount validation
            body("aTotalAmount")
                .notEmpty()
                .withMessage("Total amount is required")
                .isFloat({ min: 0 })
                .withMessage("Total amount must be a positive number"),

            // Amount difference validation (optional)
            body("aAmountDifference")
                .optional()
                .isFloat()
                .withMessage("Amount difference must be a number"),

            // Total employees validation (optional)
            body("aTotalEmployees")
                .optional()
                .isInt({ min: 0 })
                .withMessage("Total employees must be a non-negative number"),

            // Employees join validation (optional)
            body("tEmployeesJoin")
                .optional()
                .isArray()
                .withMessage("Employees join must be an array")
                .custom((value) => {
                    if (!value.every((id: string) => /^[0-9a-fA-F]{24}$/.test(id))) {
                        throw new Error("Invalid employee ID format in join list");
                    }
                    return true;
                }),

            // Employees left validation (optional)
            body("tEmployeesLeft")
                .optional()
                .isArray()
                .withMessage("Employees left must be an array")
                .custom((value) => {
                    if (!value.every((id: string) => /^[0-9a-fA-F]{24}$/.test(id))) {
                        throw new Error("Invalid employee ID format in left list");
                    }
                    return true;
                }),

            // Approved by validation (optional)
            body("tApprovedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid approver ID format"),

            // Organization validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for salary history creation/update
     */
    public static getSalaryHistoryValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // User ID validation
            body("tIdUser")
                .notEmpty()
                .withMessage("User ID is required")
                .isMongoId()
                .withMessage("Invalid user ID format"),

            // Salaries validation
            body("tSalaries")
                .isArray()
                .withMessage("Salaries must be an array"),

            // Validate each salary record
            body("tSalaries.*.tSalary")
                .notEmpty()
                .withMessage("Salary data is required")
                .isObject()
                .withMessage("Salary data must be an object"),

            // Start date validation (optional)
            body("tSalaries.*.dStartDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid start date format")
                .toDate(),

            // End date validation (optional)
            body("tSalaries.*.dEndDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid end date format")
                .toDate()
                .custom((value, { req }) => {
                    if (value && req.body.dStartDate && new Date(value) <= new Date(req.body.dStartDate)) {
                        throw new Error('End date must be after start date');
                    }
                    return true;
                }),

            // Organization validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
