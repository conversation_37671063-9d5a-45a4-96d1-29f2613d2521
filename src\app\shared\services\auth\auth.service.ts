import { HttpResponse } from '@angular/common/http';
import { computed, inject, Injectable, signal } from '@angular/core';
import { IUserData } from '@shared/models';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { ILoginRequest } from 'src/app/modules/login/model/Loging';
import { AuthApiService } from './auth.api.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly _authApiService = inject(AuthApiService);
  private readonly user = signal<IUserData | null>(null);
  readonly isLoggedIn = computed(() => !!this.user());

  login(data: ILoginRequest): Observable<IUserData> {
    return this._authApiService.login(data).pipe(
      tap((res: HttpResponse<any>) => {
        debugger;
        // const token = res.headers.get('Authorization'); // adjust header name if needed
        const userData = res.body?.data;

        if (userData) {
          localStorage.setItem('access_token', userData.sAccessToken);
        }

        if (userData) {
          this.user.set(userData);
          localStorage.setItem('user', JSON.stringify(userData));
        }
      }),
      map((res) => res.body.data)
    );
  }

  logout(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
    this.user.set(null);
  }
}
