import { model, Model, models, Schema } from 'mongoose';
import { IUserGroup } from '../../interfaces/user/user-group.interface';

/**
 * UserGroup model class that defines the schema and provides access to the UserGroup collection.
 * 
 * @class UserGroup
 * @description Handles the schema definition and model creation for User Groups
 */
export default class UserGroup {
    /**
     * Mongoose schema definition for UserGroup
     * @private
     */
    private static readonly _schema: Schema<IUserGroup> = new Schema<IUserGroup>(
        {
            sName: {
                type: String,
                required: [true, "Name must be required!"],
                trim: true,
                index: true // Indexed for faster search and filtering by group name
            },
            tAccess: {
                type: [Schema.Types.Mixed],
                default: []
            },
            sTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by group tag
            },
            aTimeMargin: {
                type: Number
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for UserGroup
     * @private
     */
    private static _model: Model<IUserGroup> = models?.usergroup || model<IUserGroup>("usergroups", UserGroup._schema);

    /**
     * Get the Mongoose model for UserGroup
     * @returns {Model<IUserGroup>} The UserGroup model
     */
    public static get model(): Model<IUserGroup> {
        return UserGroup._model;
    }
}