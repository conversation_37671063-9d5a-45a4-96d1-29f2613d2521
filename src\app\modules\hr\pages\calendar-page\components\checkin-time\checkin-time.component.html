<div *ngIf="checkin()||duration()||checkout()" class="flex justify-center items-center gap-2">
  <span class="text-red-500 font-semibold">{{ checkin() }}</span>
  <div class="w-10 flex justify-center items-center">
    <div class="rounded-full w-[5px] h-[5px] bg-gray-400"></div>
    <div class="w-8 h-[1px] bg-gray-400"></div>
  </div>
  <span class="text-gray-500">{{ duration() }}</span>
  <div class="w-10 flex justify-center items-center">
      <div class="w-8 h-[1px] bg-gray-400"></div>
      <div class="rounded-full w-[5px] h-[5px] bg-gray-400"></div>
  </div>
  <span class="text-yellow-600 font-semibold">{{ checkout() }}</span>
</div>
<div *ngIf="!checkin()&&!duration()&&!checkout()" class="flex justify-center items-center gap-2">
  <span class="text-gray-500">—</span>
</div>
