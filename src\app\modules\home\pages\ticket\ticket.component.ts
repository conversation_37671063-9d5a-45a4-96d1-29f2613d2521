import { Component } from '@angular/core';
import { TicketLeftPanelComponent } from "./components/ticket-left-panel/ticket-left-panel.component";
import { TicketRightPanelComponent } from "./components/ticket-right-panel/ticket-right-panel.component";
import { TICKETS_DUMMY_DATA } from 'src/assets/dummy_data/ticket';
import { ITicket } from './models/ticket';

@Component({
  selector: 'app-ticket',
  imports: [TicketLeftPanelComponent, TicketRightPanelComponent],
  templateUrl: './ticket.component.html',
  styleUrl: './ticket.component.scss'
})
export class TicketComponent {
  selectedTicketId: number = 0;
  tickets = TICKETS_DUMMY_DATA;
  showTicketDetails: boolean = false;
  currentSelectedTicket: ITicket = this.tickets[0];
  navigateToTicket(ticketId: number) {
    console.log(ticketId);
    this.selectedTicketId = ticketId;
    this.showTicketDetails = true;
    this.currentSelectedTicket = this.tickets.find(ticket => ticket.id === ticketId) ?? this.tickets[0];
  }
} 
