import { Schema, model, Model, models, Types } from 'mongoose';
import { IShift } from '../../interfaces/organization/shift.interface';

/**
 * Shifts model class that defines the schema and provides access to the Shifts collection.
 * 
 * @class Shifts
 * @description Handles the schema definition and model creation for Shifts
 */
export default class Shifts {
    /**
     * Mongoose schema definition for Shifts
     * @private
     */
    private static _schema: Schema<IShift> = new Schema<IShift>(
        {
            sName: {
                type: String,
                required: [true, 'Shift Name is missing!'],
                trim: true,
                index: true // Indexed for faster search and filtering by shift name
            },
            sTimezone: {
                type: String,
                required: [true, 'Timezone is missing!'],
                trim: true
            },
            sTime: {
                type: String,
                required: [true, 'Time is missing!'],
                trim: true
            },
            sPunchInTime: {
                type: String
            },
            sPunchOutTime: {
                type: String
            },
            sTimeBuffer: {
                type: String
            },
            isDefault: {
                type: Boolean,
                default: false,
                index: true // Indexed for filtering default shifts
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for Shifts
     * @private
     */
    private static _model: Model<IShift> = models?.Shift || model<IShift>("shift", Shifts._schema);

    /**
     * Get the Mongoose model for Shifts
     * @returns {Model<IShift>} The Shifts model
     */
    public static get model(): Model<IShift> {
        return Shifts._model;
    }
}