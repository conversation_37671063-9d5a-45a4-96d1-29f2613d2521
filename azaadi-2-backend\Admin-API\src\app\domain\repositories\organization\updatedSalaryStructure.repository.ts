import { IUpdatedSalaryStructure } from "../../interfaces/organization/salaryStructure.interface";
import { UpdatedSalaryStructure } from "../../models";
import Repository from "../repository";
import IUpdatedSalaryStructureRepository from "./abstracts/updatedSalaryStructureRepository.abstract";

export default class UpdatedSalaryStructureRepository extends Repository<IUpdatedSalaryStructure> implements IUpdatedSalaryStructureRepository {
    constructor() {
        super(UpdatedSalaryStructure.model);
    }
}