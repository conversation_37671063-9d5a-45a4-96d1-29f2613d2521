import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { IHoliday } from '@shared/models';
import { DialogModule } from 'primeng/dialog';
import { MonthColorPipe } from '../../../../../pipes/month-color.pipe';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-holiday-list-modal',
  imports: [CommonModule, DialogModule, MonthColorPipe],
  templateUrl: './holiday-list-modal.component.html',
  styleUrl: './holiday-list-modal.component.scss',
})
export class HolidayListModalComponent {
  private readonly _config: DynamicDialogConfig = inject(DynamicDialogConfig);
  holidays: IHoliday[] = this._config.data.holidays;

  isHolidayPassed(date: Date): boolean {
    return false; // Placeholder for actual logic to determine if the holiday has passed
  }
}
