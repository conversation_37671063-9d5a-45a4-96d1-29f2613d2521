import { Pipe, PipeTransform } from '@angular/core';
import { AttendanceStatus } from '../models/Attendance';

@Pipe({
  name: 'attendaceSymbolColor',
})
export class AttendaceSymbolColorPipe implements PipeTransform {
  transform(status: AttendanceStatus): string {
    console.log('Attendance status:', status);

    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'text-[#30CA8C]';
      case AttendanceStatus.Leave:
        return 'text-[#FF7439]';
      case AttendanceStatus.PUBLIC_HOLIDAY:
        return 'text-[#3730CA]';
      case AttendanceStatus.ORGANIZATION_WEEKEND:
        return 'text-gray-500';
      case AttendanceStatus.ERROR:
        return 'text-[#FF0000]';
      case AttendanceStatus.LHD:
        return 'text-cyan-400';
      case AttendanceStatus.HD:
        return 'text-indigo-400';
      case AttendanceStatus.TODAYS_DATE:
        return 'text-[var(--p-primary-600)] outline outline-1 outline-offset-2 outline-[var(--p-primary-500)] rounded-full';

      default:
        return 'text-[var(--p-primary-600)]';
    }
  }
}
