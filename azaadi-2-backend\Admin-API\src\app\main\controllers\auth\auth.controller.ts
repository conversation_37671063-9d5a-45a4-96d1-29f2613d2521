import { StatusCodes, ReasonPhrases } from "http-status-codes";
import { SuccessResponse } from "../../../../types/core.types";
import { IUserAuthCredentialsDTO, IUserAuthenticateResponseDTO } from "../../../DTOs/user/user.dto";
import AuthService from "../../../services/auth/auth.service";
import IAuthService from "../../../services/auth/authService.abstract";
import AsyncUtils from "../../../../utils/async.utils";
import { Request, Response } from "express";

export default class AuthController {
    private _authService: IAuthService;

    constructor(){
        this._authService = new AuthService();
    }

    async authenticateUser(req: Request, res: Response): Promise<void> {
        const response: SuccessResponse<IUserAuthenticateResponseDTO> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: await AsyncUtils.wrapFunction(this._authService.authenticateUser.bind(this._authService), [req.body.data as IUserAuthCredentialsDTO])
        }
        res.status(response.statusCode).json(response);
    }
}