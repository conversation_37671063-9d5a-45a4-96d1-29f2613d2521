import { IManageRoleRequest } from "../../interfaces/organization/manageRequest.interface";
import { ManageRoleRequest } from "../../models";
import Repository from "../repository";
import IManageRoleRequestRepository from "./abstracts/manageRoleRequestRepository.abstract";

export default class ManageRoleRequestRepository extends Repository<IManageRoleRequest> implements IManageRoleRequestRepository {
    constructor() {
        super(ManageRoleRequest.model);
    }
}