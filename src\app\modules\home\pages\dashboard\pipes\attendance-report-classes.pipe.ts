import { Pipe, PipeTransform } from '@angular/core';
import { AttendanceStatus } from '../models/Attendance';

@Pipe({
  name: 'attendanceReportClasses',
})
export class AttendanceReportClassesPipe implements PipeTransform {
  transform(status: AttendanceStatus | undefined): string {
    if (!status) return '';
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'bg-[#30CA8C] text-white'; // #30CA8C → closest: green-500
      case AttendanceStatus.Leave:
        return 'bg-[#FF7439] text-white'; // #FF7439 → closest: orange-500
      case AttendanceStatus.PUBLIC_HOLIDAY:
        return 'bg-[#3730CA] text-white'; // #3730CA → closest: blue-700
      case AttendanceStatus.ORGANIZATION_WEEKEND:
        return 'bg-gray-500 text-white'; // #D2D2D2 → closest: gray-300
      case AttendanceStatus.ERROR:
        return 'bg-red-600 text-white'; // #FF0000 → closest: red-600
      case AttendanceStatus.LHD:
        return 'bg-cyan-400 text-white'; // #00BCD4 → closest: cyan-400
      case AttendanceStatus.HD:
        return 'bg-indigo-500 text-white'; // #3F51B5 → closest: indigo-500
      case AttendanceStatus.TODAYS_DATE:
        return 'bg-[var(--p-primary-600)] text-white  outline outline-1 outline-offset-2 outline-[var(--p-primary-500)]'; // #FFD9016B → yellow with opacity
      default:
        return 'bg-[var(--p-content-background)] text-white'; // fallback
    }
  }
}
