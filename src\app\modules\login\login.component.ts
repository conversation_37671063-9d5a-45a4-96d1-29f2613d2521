import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '@shared/services/auth/auth.service';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { FloatLabel } from 'primeng/floatlabel';
import { InputTextModule } from 'primeng/inputtext';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ILoginRequest } from './model/Loging';
@Component({
  selector: 'app-login',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    ButtonModule,
    FloatLabel,
    ProgressSpinnerModule,
    CommonModule,
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent {
  loginForm: FormGroup;
  private readonly _authService = inject(AuthService);
  private readonly _router = inject(Router);
  private readonly _messageService = inject(MessageService);
  isLoading = false;

  constructor(private readonly fb: FormBuilder) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required],
    });
  }

  onLogin() {
    if (this.loginForm.valid) {
      const loginData: ILoginRequest = {
        data: {
          sEmail: this.loginForm.value['email'],
          sPassword: this.loginForm.value['password'],
        },
      };
      this.isLoading = true;
      this._authService.login(loginData).subscribe({
        next: (res) => {
          this._messageService.add({
            severity: 'success',
            life: 3000,
            summary: 'Success',
            detail: 'Login successful',
          });
          this._router.navigate(['/']);
        },
        error: (err) => {
          this._messageService.add({
            severity: 'error',
            life: 3000,
            summary: 'Error',
            detail: err.error.message,
          });
          this.isLoading = false;
        },
        complete: () => {
          this.isLoading = false;
        },
      });
    }
  }
}
