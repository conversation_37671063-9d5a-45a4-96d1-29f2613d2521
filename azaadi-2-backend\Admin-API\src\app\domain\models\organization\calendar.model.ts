import { model, Model, models, Schema } from "mongoose";
import { ICalendarTemplate } from "../../interfaces/organization/calendar.interface";

/**
 * Calendar model class that defines the schema and provides access to the Calendar collection.
 * 
 * @class Calendar
 * @description Handles the schema definition and model creation for Calendar Templates
 */
export default class Calendar {
    /**
     * Mongoose schema definition for Calendar
     * @private
     */
    private static _schema = new Schema<ICalendarTemplate>(
        {
            sName: {
                type: String,
                trim: true,
                index: true // Indexed for faster search and filtering by calendar name
            },
            sTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by calendar tag
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                required: [true, 'Organization is missing!'],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for Calendar
     * @private
     */
    private static _model: Model<ICalendarTemplate> = models?.Calendar || model<ICalendarTemplate>("calendartemplate", Calendar._schema);

    /**
     * Get the Mongoose model for Calendar
     * @returns {Model<ICalendarTemplate>} The Calendar model
     */
    public static get model(): Model<ICalendarTemplate> {
        return Calendar._model;
    }
}