import { Component, EventEmitter, Output } from '@angular/core';
import { PanelModule } from 'primeng/panel';
import { DatePickerModule } from 'primeng/datepicker';
import { FormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { IftaLabelModule } from 'primeng/iftalabel';
import { TextareaModule } from 'primeng/textarea';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-work-from-home',
  imports: [
    PanelModule,
    DatePickerModule,
    FormsModule,
    InputNumberModule,
    IftaLabelModule,
    TextareaModule,
    InputTextModule,
    ButtonModule,
  ],
  templateUrl: './work-from-home.component.html',
})
export class WorkFromHomeComponent {
  @Output() isWorkFromHomeDrawerVisibleChange = new EventEmitter<boolean>();
  startDate: Date | undefined;
  endDate: Date | undefined;
  totalDays: number | undefined = 0;
  reason: string = '';
  searchedEmployee: string = '';

  calculateTotalDays = () => {
    if (this.startDate && this.endDate) {
      const start = new Date(this.startDate);
      const end = new Date(this.endDate);
      const timeDiff = end.getTime() - start.getTime();
      if (timeDiff < 0) {
        this.startDate = undefined;
        this.endDate = undefined;
        return 0;
      }
      const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
      return (this.totalDays = diffDays);
    }
    return 0;
  };

  closeWorkFromHomeDrawer() {
    this.isWorkFromHomeDrawerVisibleChange.emit(false);
  }
}
