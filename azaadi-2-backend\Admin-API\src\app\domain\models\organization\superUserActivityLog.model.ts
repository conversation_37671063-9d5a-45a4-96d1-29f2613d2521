import { model, Model, models, Schema } from "mongoose";
import { ISuperUserActivityLog } from "../../interfaces/organization/manageLog.interface";
import { EmsActivityLogEntityType } from "../../../../utils/meta/enum.utils";

/**
 * SuperUserActivityLog model class that defines the schema and provides access to the SuperUserActivityLog collection.
 * 
 * @class SuperUserActivityLog
 * @description Handles the schema definition and model creation for Super User Activity Logs
 */
export default class SuperUserActivityLog {
    /**
     * Mongoose schema definition for SuperUserActivityLog
     * @private
     */
    private static _schema: Schema<ISuperUserActivityLog> = new Schema<ISuperUserActivityLog>(
        {
            sEntityType: {
                type: String,
                required: [true, "Entity type is required"],
                enum: EmsActivityLogEntityType,
                index: true // Indexed for filtering by entity type
            },
            tCreatedBy: {
                type: Schema.Types.ObjectId,
                ref: "login",
                required: [true, "Created by is required"],
                trim: true,
                index: true // Indexed for faster lookups by creator
            },
            tEntityIds: {
                type: [Schema.Types.ObjectId],
                required: [true, "Entity IDs are required"],
                index: true // Indexed for faster lookups by entities
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for SuperUserActivityLog
     * @private
     */
    private static _model: Model<ISuperUserActivityLog> = models?.SuperUserActivityLog || model<ISuperUserActivityLog>("superuseractivitylog", SuperUserActivityLog._schema);

    /**
     * Get the Mongoose model for SuperUserActivityLog
     * @returns {Model<ISuperUserActivityLog>} The SuperUserActivityLog model
     */
    public static get model(): Model<ISuperUserActivityLog> {
        return SuperUserActivityLog._model;
    }
}