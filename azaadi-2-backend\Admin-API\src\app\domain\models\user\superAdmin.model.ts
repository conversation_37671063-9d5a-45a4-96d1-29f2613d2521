import { model, Model, models, Schema } from "mongoose";
import { ISuperAdmin } from "../../interfaces/user/user.interface";
import { PasswordMiddleware } from "../../../../middlewares/password.middleware";

/**
 * SuperAdmin model class that defines the schema and provides access to the SuperAdmin collection.
 * Handles super admin users with enhanced privileges across the system.
 * 
 * @class SuperAdmin
 * @description Handles the schema definition and model creation for Super Admins
 */
export default class SuperAdmin {
    /**
     * Mongoose schema definition for SuperAdmin
     * @private
     */
    private static readonly _schema = new Schema<ISuperAdmin>(
        {
            sName: {
                type: String,
                trim: true,
                index: true // Indexed for faster search and filtering by name
            },
            sEmail: {
                type: String,
                required: [true, "Email must be required!"],
                trim: true,
                unique: true,
                index: true // Indexed for faster lookups and uniqueness constraint
            },
            sPassword: {
                type: String,
                required: [true, "Password must be required!"],
            },
            aPhoneNumber: {
                type: Number,
                required: [true, "Phone number must be required!"],
                index: true // Indexed for phone number lookups
            },
            sProfileUrl: {
                type: String,
                trim: true
            },
            bIsActive: {
                type: Boolean,
                default: true,
                index: true // Indexed for active/inactive filtering
            },
            bCanLogin: {
                type: Boolean,
                default: true,
                index: true // Indexed for login status filtering
            },
            bIsDeveloper: {
                type: Boolean,
                default: false,
                index: true // Indexed for developer status filtering
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Text Index Configuration:
     * Creates a compound text index on sName and sEmail fields for full-text search capabilities
     * This enables efficient text-based searches with relevance scoring
     * 
     * Use cases:
     * 1. Search admins by partial name or email
     * 2. Find admins using natural language queries
     * 3. Get results sorted by text relevance
     * 
     * Query Examples:
     * // Search for admins with name or email containing "john"
     * await SuperAdmin.model.find({ $text: { $search: "john" }})
     * 
     * // Search with relevance scoring
     * await SuperAdmin.model.find(
     *   { $text: { $search: "john developer" }},
     *   { score: { $meta: "textScore" }}
     * ).sort({ score: { $meta: "textScore" }})
     * 
     * Weights Configuration:
     * - sEmail weight=3: Email matches are more important (primary identifier)
     * - sName weight=2: Name matches are secondary
     */
    static {
        // Text index for full-text search across name and email
        SuperAdmin._schema.index(
            { sName: 'text', sEmail: 'text' },
            {
                weights: {
                    sName: 2,
                    sEmail: 3
                },
                name: 'super_admin_text_index'
            }
        );

        // Add password hashing middleware
        new PasswordMiddleware().addHashingMiddleware(SuperAdmin._schema);
    }

    /**
     * Mongoose model for SuperAdmin
     * @private
     */
    private static _model: Model<ISuperAdmin> = models?.superadmin || model<ISuperAdmin>("superadmin", SuperAdmin._schema);

    /**
     * Get the Mongoose model for SuperAdmin
     * @returns {Model<ISuperAdmin>} The SuperAdmin model
     */
    public static get model(): Model<ISuperAdmin> {
        return SuperAdmin._model;
    }
}