import { IManageUserRequest } from "../../interfaces/organization/manageRequest.interface";
import { ManageUserRequest } from "../../models";
import Repository from "../repository";
import IManageUserRequestRepository from "./abstracts/manageUserRequestRepository.abstract";

export default class ManageUserRequestRepository extends Repository<IManageUserRequest> implements IManageUserRequestRepository {
    constructor() {
        super(ManageUserRequest.model);
    }
}