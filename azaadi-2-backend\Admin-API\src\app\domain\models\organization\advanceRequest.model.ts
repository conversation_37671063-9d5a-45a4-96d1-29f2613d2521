import { model, Model, models, Schema, Types } from "mongoose";
import { IAdvanceRequest, IAdvanceMonthlyDetail, IAttachment } from "../../interfaces/organization/manageRequest.interface";
import { EMSAdvanceStatus, EMSDeductType } from "../../../../utils/meta/enum.utils";

/**
 * AdvanceRequest model class that defines the schema and provides access to the AdvanceRequest collection.
 * 
 * @class AdvanceRequest
 * @description Handles the schema definition and model creation for Advance Requests
 */
export default class AdvanceRequest {
    /**
     * Mongoose schema definition for monthly details in advance requests
     * @private
     */
    private static _monthlyDetailsSchema = new Schema<IAdvanceMonthlyDetail>(
        {
            aMonth: {
                type: Number,
                required: [true, 'Month is required'],
                index: true // Indexed for filtering by month
            },
            aYear: {
                type: Number,
                required: [true, 'Year is required'],
                index: true // Indexed for filtering by year
            },
            aAmount: {
                type: Number,
                required: [true, 'Amount is required']
            },
            bIsDeducted: {
                type: Boolean,
                default: false
            }
        },
        {
            timestamps: true,
            _id: true // Maintain separate IDs for monthly details
        }
    );

    /**
     * Mongoose schema definition for attachments in advance requests
     * @private
     */
    private static _attachmentSchema = new Schema<IAttachment>(
        {
            sFilename: {
                type: String,
                required: [true, 'Filename is required'],
                trim: true
            },
            sPath: {
                type: String,
                required: [true, 'File path is required'],
                trim: true
            }
        },
        {
            timestamps: true,
            _id: true // Maintain separate IDs for attachments
        }
    );

    /**
     * Mongoose schema definition for AdvanceRequest
     * @private
     */
    private static _schema = new Schema<IAdvanceRequest>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                ref: "login",
                required: [true, "Id user must be required!"],
                trim: true,
                index: true // Indexed for faster lookups by user
            },
            aTotalAmount: {
                type: Number,
                default: 0
            },
            eStatus: {
                type: String,
                enum: EMSAdvanceStatus,
                default: EMSAdvanceStatus.REQUEST,
                index: true // Indexed for filtering by status
            },
            eDeductType: {
                type: String,
                enum: EMSDeductType
            },
            dStartDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            dEndDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            monthlyDetails: {
                type: [AdvanceRequest._monthlyDetailsSchema],
                default: []
            },
            tHrApprovedBy: {
                type: Schema.Types.ObjectId,
                ref: "login",
                trim: true,
                index: true // Indexed for filtering by HR approver
            },
            tAccApprovedBy: {
                type: Schema.Types.ObjectId,
                ref: "login",
                trim: true,
                index: true // Indexed for filtering by accounts approver
            },
            dDisbursementDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            dApproveDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            sReason: {
                type: String,
                trim: true
            },
            sReply: {
                type: String,
                trim: true
            },
            tAttachments: {
                type: [AdvanceRequest._attachmentSchema],
                default: []
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            },
            aDisbursementMonth: {
                type: Number,
                index: true // Indexed for filtering by disbursement month
            },
            aDisbursementYear: {
                type: Number,
                index: true // Indexed for filtering by disbursement year
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for AdvanceRequest
     * @private
     */
    private static _model: Model<IAdvanceRequest> = models?.AdvanceRequest || model<IAdvanceRequest>("advanceRequest", AdvanceRequest._schema);

    /**
     * Get the Mongoose model for AdvanceRequest
     * @returns {Model<IAdvanceRequest>} The AdvanceRequest model
     */
    public static get model(): Model<IAdvanceRequest> {
        return AdvanceRequest._model;
    }
}