<p-card>
  <div class="h-[200px] flex flex-col justify-between">
    <ng-container *ngIf="!isLoading; else elseBlock">
      <div class="flex justify-between items-center">
        <h1 class="text-xl font-semibold">
          {{ "dashboard.leaveBalance.title" | translate }}
        </h1>
        <p-button
          [variant]="'text'"
          [size]="'small'"
          [label]="'dashboard.leaveBalance.requestLeave' | translate"
        />
      </div>
      <div class="flex flex-col justify-between">
        <div class="flex justify-between">
          <div
            *ngFor="let item of leaveBalances"
            class="text-center grid grid-cols-1"
          >
            <app-circular-progress
              [label]="item.aCount - item.aRemaining"
              [size]="70"
            />
          </div>
        </div>
        <div class="flex justify-between">
          <div
            *ngFor="let item of leaveBalances"
            class="text-center grid grid-cols-1"
          >
            <div class="mt-4">
              <p class="font-semibold text-[13px]">
                {{ getLeaveTypeName(item.leaveType) }}
              </p>
              <p class="text-[12px]">
                {{ "dashboard.leaveBalance.total" | translate }} -
                {{ item.aCount }}
              </p>
              <p class="text-[12px]">
                {{ "dashboard.leaveBalance.remaining" | translate }} -
                {{ item.aRemaining }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-template #elseBlock>
      <p-progress-spinner ariaLabel="loading" />
    </ng-template>
  </div>
</p-card>
