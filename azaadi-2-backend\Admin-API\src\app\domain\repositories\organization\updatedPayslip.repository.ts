import { IUpdatedPayslip } from "../../interfaces/organization/payslip.interface";
import { UpdatedPayslip } from "../../models";
import Repository from "../repository";
import IUpdatedPayslipRepository from "./abstracts/updatedPayslipRepository.abstract";

export default class UpdatedPayslipRepository extends Repository<IUpdatedPayslip> implements IUpdatedPayslipRepository {
    constructor() {
        super(UpdatedPayslip.model);
    }
}