/* styles.css */
::ng-deep .custom-tab-menu .p-tabmenu-nav {
  @apply flex bg-white rounded-xl shadow-md overflow-hidden;
}

::ng-deep .custom-tab-menu .p-tabmenu-nav .p-tabmenuitem {
  @apply transition duration-200 ease-in-out;
}

::ng-deep .custom-tab-menu .p-tabmenu-nav .p-tabmenuitem a {
  @apply block px-4 text-sm font-medium text-gray-700;
}

::ng-deep .custom-tab-menu .p-tabmenu-nav .p-highlight a {
  @apply text-white bg-teal-500 font-semibold;
}
::ng-deep .identity .p-card {
  height: 100%;
}

::ng-deep .identity .p-card-body {
  display: flex;
  flex-direction: column;
  height: 100%;
}

::ng-deep .identity .p-card-content {
  flex: 1;
}
button{
    @apply rounded-full bg-[#239595] border-none shadow-md;
}


::ng-deep .custom-tab-menu .p-tabmenuitem {
  position: relative;
  padding-bottom: 8px;
  transition: color 0.3s ease;
}

::ng-deep .custom-tab-menu .p-tabmenuitem::after {
  content: "";
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  transform-origin: center;
  height: 4px;
  width: 60px;
  background-color: var(--tab-underline-color, #41bdbd);
  transition: transform 0.3s ease-in-out;
  border-radius: 4px;
}

::ng-deep .custom-tab-menu .p-tabmenuitem:hover::after,
::ng-deep .custom-tab-menu .p-tabmenuitem.p-highlight::after {
  transform: translateX(-50%) scaleX(1);
}
