{"name": "ems", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "extract-translations": "ngx-translate-extract -i ./src -o ./src/assets/i18n/en.json -o ./src/assets/i18n/fr.json --clean --sort --format namespaced-json --marker _"}, "private": true, "dependencies": {"@angular/animations": "^19.1.0", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/forms": "^19.1.0", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/router": "^19.1.0", "@ckeditor/ckeditor5-angular": "^9.1.0", "@colsen1991/ngx-translate-extract-marker": "^3.0.1", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@primeng/themes": "^19.0.9", "ckeditor5": "^45.0.0", "primeicons": "^7.0.0", "primeng": "^19.0.9", "quill": "^2.0.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.3", "@angular/cli": "^19.1.3", "@angular/compiler-cli": "^19.1.0", "@types/jasmine": "~5.1.0", "@vendure/ngx-translate-extract": "^9.4.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.7.2"}}