<mat-nav-list>
  @for (item of menu; track $index) { @if (item.isAccess) {
  <span>
    @if (item.children && item.children.length > 0) {
    <span>
      <mat-accordion>
        <mat-expansion-panel class="tree-sidebar" [expanded]="isChildOpen(item.children)" >
          <mat-expansion-panel-header>
            <mat-panel-title>
              <ng-container
                [ngTemplateOutlet]="ListOfData"
                [ngTemplateOutletContext]="{ item: item }"
              >
              </ng-container>
            </mat-panel-title>
          </mat-expansion-panel-header>
          @for (child of item.children; track $index) { @if (child.isAccess){
          <span class="menu">
            <mat-list-item  (click)="onMenuOptionClick(child.route!)" [class]="activeUrl==child.route?'active':''">
              <ng-container
                [ngTemplateOutlet]="ListOfData"
                [ngTemplateOutletContext]="{ item: child }"
              >
              </ng-container>
            </mat-list-item>
          </span>
          } }
        </mat-expansion-panel>
      </mat-accordion>
    </span>
    } @else if (!item.children || item.children.length === 0) {
    <span class="menu">
      @if (item.route) {
      <mat-list-item (click)="onMenuOptionClick(item.route)" [class]="activeUrl==item.route?'active':''">
        <ng-container
          [ngTemplateOutlet]="ListOfData"
          [ngTemplateOutletContext]="{ item: item }"
        >
        </ng-container>
      </mat-list-item>
      } @else {
      <ng-container
        [ngTemplateOutlet]="ListOfData"
        [ngTemplateOutletContext]="{ item: item }"
      >
      </ng-container>
      }
    </span>
    }
  </span>
  } }
</mat-nav-list>

<ng-template #ListOfData let-item="item">
  <div class="link">
    @if (item.iconName) {
    <mat-icon>{{ item.iconName }}</mat-icon>
    }@else {
      <div></div>
    }
    <div>
      <span>{{ item.displayName }}</span>
    </div>
  </div>
</ng-template>
