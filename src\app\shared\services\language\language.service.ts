import { Injectable, Signal, WritableSignal, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ILanguage } from '../../models/Language';
import { environment } from '../../../../environments/environment';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class LanguageService {
  private readonly _userLanguageKey: string = 'userLang';
  private readonly _selectedLanguage: WritableSignal<ILanguage> = signal({
    name: 'English',
    code: 'en',
  });
  /**
   * Getter method to retrieve the selected language.
   * @returns The selected language.
   */
  public get selectedLanguage(): Signal<ILanguage> {
    return this._selectedLanguage;
  }

  /**
   * Sets the selected language.
   * @param value - The language to be set as selected.
   */
  public set selectedLanguage(value: string) {
    const language = this._languages.find((lang) => lang.code === value);
    if (!language) return;
    this._selectedLanguage.set(language);
  }

  private _defaultLanguage: string = 'fr';
  private _languages: ILanguage[] = [];

  /**
   * Sets the languages.
   *
   * @param {ILanguage[]} value - An array of ITxLanguage objects to set.
   */
  public set languages(value: ILanguage[]) {
    this._languages = value;
  }

  public get languages(): ILanguage[] {
    return this._languages;
  }
  constructor(
    private readonly _translate: TranslateService,
    private readonly _http: HttpClient
  ) {}

  /**
   * Loads the default language and available languages from the environment configuration.
   * Retrieves the user's language preference from local storage if available.
   * Sets the application language to the user's preference or the default language.
   */
  public load() {
    this._defaultLanguage = environment?.defaultLanguage.code;
    this.languages = environment.languages;

    const localLanguageCode = localStorage.getItem(this._userLanguageKey);
    let languageCode = this._defaultLanguage;
    if (localLanguageCode) {
      languageCode = localLanguageCode;
    }
    this.changeLanguage(languageCode);
  }

  /**
   * Changes the language of the application to the specified language.
   * Updates the selected language, stores it in local storage, fetches translations,
   * applies translations, and activates the selected language.
   *
   * @param lang - The language code to switch to.
   */
  public changeLanguage(lang: string) {
    this.selectedLanguage = lang;

    localStorage.setItem(this._userLanguageKey, lang);
    this._http.get(`assets/i18n/${lang}.json`).subscribe((translations) => {
      this._translate.setTranslation(lang, translations); // Apply translations
      this._translate.use(lang); // Activate the selected language
    });
  }
}
