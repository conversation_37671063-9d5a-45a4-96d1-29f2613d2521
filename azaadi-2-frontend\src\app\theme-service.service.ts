import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs'

export enum Theme {
  Light = 'light',
  Dark = 'dark'
}
@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private _theme = new BehaviorSubject<Theme>(Theme.Light);
  readonly theme$ = this._theme.asObservable();

  setTheme(theme: Theme) {
    this._theme.next(theme);
  }

  getTheme() {
    return this._theme.value;
  }
}
