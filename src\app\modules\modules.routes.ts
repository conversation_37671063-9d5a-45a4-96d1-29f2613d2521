import { Routes } from '@angular/router';

export const MODULE_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./modules.component').then((m) => m.ModulesComponent),
    children: [
      {path: '', pathMatch: 'full', redirectTo: 'home'},
      {
        path: 'admin',
        loadChildren: () =>
          import('./admin/admin.routes').then((m) => m.ADMIN_ROUTES),
      },
    
      {
        path: 'home',
        loadChildren: () => import('./home/<USER>').then((m) => m.HOME_ROUTES),
      },
      {
        path: 'hr',
        loadChildren: () =>
          import('./hr/hr.route').then((m) => m.CALENDAR_ROUTES),
      },
      {
        path: 'accounts',
        loadChildren: () =>
          import('./accounts/acounts.route').then((m) => m.ACCOUNTS_ROUTES),
      }
    ]
  },

];
