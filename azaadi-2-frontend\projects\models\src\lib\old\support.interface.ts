import { emsSupportCategory, emsSupportPriority, emsSupportType } from "../enums/support.enum";
import { EmsBranches, EmsOrganization } from "./manageOrganization.interface";
import { EmsUser } from "./user.interface";

export interface EmsItSupport {
    _id?: string;
    createdAt?: Date;
    updatedAt?: Date;
    aTicketId?: number;
    tRequestBy?: EmsUser | string;
    eType?: emsSupportType;
    tWatchers?: EmsUser[] | string[];
    ePriority?: emsSupportPriority;
    eCategory?: emsSupportCategory;
    tBranch?: EmsBranches | string;
    tOrganization?: EmsOrganization | string;
    sTitle?: string;
    sDescription?: string;
    dRequestDate?: Date;
    sUrl?: string[];
    bIsSolved?: boolean;
}

export interface EmsSupportChat {
    _id?: string;
    createdAt?: Date;
    updatedAt?: Date;
    tItSupport?: EmsItSupport | string;
    tSendBy?: EmsUser | string;
    sMessage?: string;
    sUrl?: string[];
}