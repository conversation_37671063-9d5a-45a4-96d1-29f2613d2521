import { NextFunction, Request, Response } from "express";
import { body, ValidationChain, validationResult } from "express-validator";

/**
 * Class to handle role-related request validations
 */
export class RoleValidator {
    /**
     * Get validation rules for role creation/update
     */
    public static getRoleValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Tag validation
            body("sTag")
                .trim()
                .notEmpty()
                .withMessage("Tag is required")
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Tag can only contain letters, numbers, hyphens and underscores")
                .isLength({ min: 2, max: 50 })
                .withMessage("Tag must be between 2 and 50 characters"),

            // Organization validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Head role validation (optional)
            body("tHead")
                .optional()
                .isMongoId()
                .withMessage("Invalid head role ID format"),

            // HR reporting role validation (optional)
            body("tHrReport")
                .optional()
                .isMongoId()
                .withMessage("Invalid HR reporting role ID format"),

            // Additional organizations validation (optional)
            body("tAdditionalOrg")
                .optional()
                .isArray()
                .withMessage("Additional organizations must be an array")
                .custom((value) => {
                    if (!Array.isArray(value)) {
                        return true; // Skip if not array (will be caught by isArray check)
                    }
                    const allValid = value.every(id => /^[0-9a-fA-F]{24}$/.test(id));
                    if (!allValid) {
                        throw new Error('All additional organization IDs must be valid MongoDB ObjectIds');
                    }
                    return true;
                }),

            // Super admin flag validation
            body("bIsCreatedBySuperAdmin")
                .optional()
                .isBoolean()
                .withMessage("IsCreatedBySuperAdmin must be a boolean")
                .default(false),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for role settings creation/update
     */
    public static getRoleSettingsValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Role validation
            body("tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // Title validation
            body("sTitle")
                .trim()
                .notEmpty()
                .withMessage("Title is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Title must be between 2 and 100 characters"),

            // Type validation (optional)
            body("sType")
                .optional()
                .trim()
                .isString()
                .withMessage("Type must be a string"),

            // Value validation (optional)
            body("sValue")
                .optional(),

            // Managed by role validation (optional)
            body("tManagedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid manager role ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for bulk role operations
     */
    public static getBulkRoleValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Validate array of role entries
            body()
                .isArray()
                .withMessage("Request body must be an array of role entries"),
            
            // Name validation for each entry
            body("*.sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Tag validation for each entry
            body("*.sTag")
                .trim()
                .notEmpty()
                .withMessage("Tag is required")
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Tag can only contain letters, numbers, hyphens and underscores")
                .isLength({ min: 2, max: 50 })
                .withMessage("Tag must be between 2 and 50 characters"),

            // Organization validation for each entry
            body("*.tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Head role validation for each entry (optional)
            body("*.tHead")
                .optional()
                .isMongoId()
                .withMessage("Invalid head role ID format"),

            // HR reporting role validation for each entry (optional)
            body("*.tHrReport")
                .optional()
                .isMongoId()
                .withMessage("Invalid HR reporting role ID format"),

            // Additional organizations validation for each entry (optional)
            body("*.tAdditionalOrg")
                .optional()
                .isArray()
                .withMessage("Additional organizations must be an array")
                .custom((value) => {
                    if (!Array.isArray(value)) {
                        return true;
                    }
                    const allValid = value.every(id => /^[0-9a-fA-F]{24}$/.test(id));
                    if (!allValid) {
                        throw new Error('All additional organization IDs must be valid MongoDB ObjectIds');
                    }
                    return true;
                }),

            // Super admin flag validation for each entry
            body("*.bIsCreatedBySuperAdmin")
                .optional()
                .isBoolean()
                .withMessage("IsCreatedBySuperAdmin must be a boolean")
                .default(false),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for bulk role settings operations
     */
    public static getBulkRoleSettingsValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Validate array of role settings entries
            body()
                .isArray()
                .withMessage("Request body must be an array of role settings entries"),
            
            // Role validation for each entry
            body("*.tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // Title validation for each entry
            body("*.sTitle")
                .trim()
                .notEmpty()
                .withMessage("Title is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Title must be between 2 and 100 characters"),

            // Type validation for each entry (optional)
            body("*.sType")
                .optional()
                .trim()
                .isString()
                .withMessage("Type must be a string"),

            // Value validation for each entry (optional)
            body("*.sValue")
                .optional(),

            // Managed by role validation for each entry (optional)
            body("*.tManagedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid manager role ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
