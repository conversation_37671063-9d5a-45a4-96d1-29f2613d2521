import { IUser } from "../../domain/interfaces/user/user.interface";
import IUserRepository from "../../domain/repositories/user/abstract/userRepository.abstract";
import UserRepository from "../../domain/repositories/user/user.repository";
import Service from "../service";
import IUserService from "./abstracts/userService.abstract";

export default class UserService extends Service<IUser, IUserRepository> implements IUserService {
    constructor() {
        super(new UserRepository());
    }
}