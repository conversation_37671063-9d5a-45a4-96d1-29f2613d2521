import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { EMSFileType } from '../../../../utils/meta/enum.utils';
import { IOrganization } from './organization.interface';

export interface IManageDocument extends IConcept {
    sName: string;
    sTag?: string;
    eType: EMSFileType[];
    bAllowMultiple: boolean;
    tOrganization: string | Types.ObjectId | IOrganization;
}