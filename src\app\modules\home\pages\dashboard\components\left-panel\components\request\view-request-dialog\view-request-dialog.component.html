<section class="layout gap-4">
  <section class="min-w-fit">
    <table class="w-full">
      <tr>
        <td class="p-2 text-black">Name:</td>
        <td class="p-2">{{ request.tSender }}</td>
      </tr>
      <tr>
        <td class="p-2 text-black">Dates:</td>
        <td class="p-2">
          <div class="flex flex-wrap gap-2">
            <span
              *ngFor="let item of request.dDate"
              class="border border-[var(--p-primary-200)] px-2 rounded-3xl"
            >
              {{ item | date : "d MMM" }}
            </span>
          </div>
        </td>
      </tr>
      <tr>
        <td class="p-2 text-black">Status:</td>
        <td class="p-2">
          <p-tag
            rounded="true"
            [severity]="getStatusSeverity()"
            [value]="getRequestStatusLabel()"
          />
        </td>
      </tr>
      <tr>
        <td class="p-2 text-black">Type:</td>
        <td class="p-2">{{ getRequestTypeLabel() }}</td>
      </tr>

      <tr>
        <td class="p-2 text-black">Approver:</td>
        <td class="p-2">{{ request.tApproverBy }}</td>
      </tr>
    </table>
  </section>
  <p-divider layout="vertical" />

  <section class="max-w-[300px]">
    <h1 class="text-xl font-semibold mb-2">{{ request.sReason }}</h1>
    <p>{{ request.sMessage }}</p>
  </section>
</section>
