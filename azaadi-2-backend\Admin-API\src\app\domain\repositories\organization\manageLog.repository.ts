import { IManageLog } from "../../interfaces/organization/manageLog.interface";
import { ManageLog } from "../../models";
import Repository from "../repository";
import IManageLogRepository from "./abstracts/manageLogRepository.abstract";

export default class ManageLogRepository extends Repository<IManageLog> implements IManageLogRepository {
    constructor() {
        super(ManageLog.model);
    }
}