import { CommonModule } from '@angular/common';
import { Component, linkedSignal, signal } from '@angular/core';
import { CarouselModule } from 'primeng/carousel';
import { IAnniversary } from '../../../../models/anniversary';
import { ANNIVERSARY_DATA } from 'src/assets/dummy_data/anniversary';
import { RelativeDatePipe } from '@shared/pipes/relative-date.pipe';

@Component({
  selector: 'app-anniversay',
  imports: [CommonModule, CarouselModule, RelativeDatePipe],
  templateUrl: './anniversay.component.html',
  styleUrl: './anniversay.component.scss'
})
export class AnniversayComponent {
    annivarsaries = signal<IAnniversary[]>(ANNIVERSARY_DATA);
    todaysAnnivarsaries = linkedSignal(() => {
      return this.annivarsaries().filter((anniversary) => {
        const today = new Date();
        const anniversaryDate = new Date(anniversary.anniversaryDate);
        return today.toDateString() === anniversaryDate.toDateString();
      });
    })
    upcomingAnnivarsaries = linkedSignal(() => {
      return this.annivarsaries().filter((anniversary) => {
        const today = new Date();
        const anniversaryDate = new Date(anniversary.anniversaryDate);
        return anniversaryDate.getTime() > today.getTime();
      });
    })
}
