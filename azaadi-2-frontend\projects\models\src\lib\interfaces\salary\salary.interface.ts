import {
  EmsAdvanceStatus,
  EmsDeductType,
  EmsGovtSub,
  EmsIncentiveType,
  EmsSalaryStructureCategory,
  EmsSkillCategory,
  EmsUpdatedSalaryStructureCategory,
} from '../../enums/managements.enum';
import {
  ICommon,
  ICommonDescription,
  ICommonFile,
  ICommonIdUser,
  ICommonMonthCount,
  ICommonName,
  ICommonOrganization,
  ICommonProfile,
  ICommonSalary,
  ICommonSID,
  ICommonStartEndDate,
  ICommonTag,
} from '../common.interface';
import { EmsUser, EmsUserDetails } from '../users/user.interface';

//For Previous Salary Structure

export interface EmsNonPfSalary extends ICommonName, ICommonDescription {
  aMonthlyGross: number;
  aBasic?: number;
  aHRA?: number;
  aConveyanceAllowance?: number;
  aBasketAllowance?: number;
  aEarnedGross?: number;
  aAdvanceOpening?: number;
  aFurtherAdvance?: number;
  aAdvanceAdjusted?: number;
  aClosing?: number;
  aMedicalInsurance?: number;
  aTDS?: number;
  aPTax?: number;
  aNetAmount?: number;
  aCTC?: number;
  aMonthDays?: number;
  aAbsent?: number;
  aPaidDays?: number;
  aMiscellaneousAmount?: number;
}
export interface EmsPfSalary extends ICommonName, ICommonDescription {
  eGovtSub?: EmsGovtSub;
  eCategory?: EmsSkillCategory;
  aMinimumWages?: number;
  bEPFApplicability?: boolean;
  bRestrictPF?: boolean;
  aVoluntaryEPF?: number;
  bESICApplicability?: boolean;
  bPTaxApplicability?: boolean;
  aMBaseRate?: number;
  aMOldBasic?: number;
  aMBasicRate?: number;
  aMHraRate?: number;
  aMConveyance?: number;
  aMBasketAllowance?: number;
  aMGrossRate?: number;
  aErnBasic?: number;
  aErnHra?: number;
  aErnConveyance?: number;
  aErnBasketAllowance?: number;
  aErnGrossEarning?: number;
  aErnIncentive?: number;
  aErnTotalEarning?: number;
  aEedPf?: number;
  aEedPfVoluntary?: number;
  aEedESI?: number;
  aEedPTax?: number;
  aEedOtherDeduction?: number;
  aEedTDS?: number;
  aEedLWF?: number;
  aNetPayable?: number;
  aAdvanceOpening?: number;
  aFurtherAdvance?: number;
  aAdvanceAdjusted?: number;
  aClosing?: number;
  aEpfAbryReimbursement?: number;
  aBankDisbursment?: number;
  aErcEmployerEPS?: number;
  aErcEmployerEPF?: number;
  aErcAdminCharges?: number;
  aErcEDLICharges?: number;
  aErcEsiEmployer?: number;
  aErcAdditional?: number;
  aErcMedicalInsurance?: number;
  aMonthlyCTC?: number;
  dDob?: Date;
  aMonthDays?: number;
  aAbsent?: number;
  aPaidDays?: number;
  aMiscellaneousAmount?: number;
}

//Updated Salary Structure

export interface EmsUpdatedSalary extends ICommon {
  aErnBasic?: number;
  aErnHra?: number;
  aErnConveyance?: number;
  aErnBasketAllowance?: number;
  aMedicalInsurance?: number;
  aEarnedGross?: number;
  aOtherDeduction?: number;
  aCTC?: number;
  aNetAmount?: number;
}
export interface EmsUpdatedNonPfSalary extends EmsUpdatedSalary {
  aTDS?: number;
}
export interface EmsUpdatedPfSalary extends EmsUpdatedSalary {
  aGrossSalary?: number;
  aErnIncentive?: number;
  aDeductOwnOf?: number;
  aDeductEmployerPf?: number;
  aDeductPTax?: number;
}
export interface EmsUpdatedEsiSalary extends EmsUpdatedPfSalary {
  aDeductESI?: number;
  aDeductESIEmployer?: number;
}
export interface EmsSalaryStructure extends ICommon, ICommonIdUser {
  tSalaryStructure?:
    | any
    | EmsNonPfSalary
    | EmsPfSalary
    | EmsUpdatedNonPfSalary
    | EmsUpdatedPfSalary
    | EmsUpdatedEsiSalary;
  eType?: EmsSalaryStructureCategory | EmsUpdatedSalaryStructureCategory;
  bHavingAdvancePayment?: boolean;
}
export interface EmsUpdatedSalaryStructure extends EmsSalaryStructure {}
export interface EmsPayslip
  extends EmsSalaryStructure,
    ICommon,
    ICommonStartEndDate,
    Omit<ICommonMonthCount, 'aAmount'> {
  aSalaryDay?: number;
  bIsReportingAuthApproved?: boolean;
  bIsHrApproved?: boolean;
  aMiscAmount?: number;
  aMiscDescription?: string;
  user?: SalUser;
  userBank?: SalUserBank;
  leaveBalance?: SalLeaveBalance[];
}

interface SalUser
  extends Omit<ICommonProfile, 'sPassword'>,
    Pick<EmsUserDetails, 'aAadharNo' | 'sName' | 'dJoinDate' | 'tIdUser'> {
  dInactiveDate?: Date;
  tDepartment?: ICommonSID;
  tDesignation?: ICommonSID;
  tIdEmployee?: IdEmployee;
  tRole?: SalRole;
}
interface IdEmployee extends Pick<ICommonSID, 'sId'> {
  aPunchId?: number;
  sCode?: string;
}

interface SalRole extends ICommonSID {
  tOrganization?: SalOrganization;
}
interface SalOrganization extends ICommonSID, Pick<ICommonTag, 'sTag'> {}
interface SalUserBank extends Pick<ICommonSID, 'sId'> {
  sBankName?: string;
  sBankAccount?: string;
  sIfscCode?: string;
  sBranchName?: string;
}
interface SalLeaveBalance {
  aCount?: number;
  sType?: string;
  sRoleId?: string;
}
export interface EmsDiffSalarySheet
  extends ICommon,
    ICommonOrganization,
    Omit<ICommonMonthCount, 'aAmount'>,
    Pick<ICommonSalary, 'aTotalAmount'> {
  aAmountDifference?: number;
  aTotalEmployees?: number;
  tEmployeesJoin?: EmsUser[] | string[];
  tEmployeesLeft?: EmsUser[] | string[];
  tApprovedBy?: EmsUser | string;
}
interface EType {
  eType: EmsIncentiveType | string;
}
export interface EmsIncentive
  extends EType,
    Omit<ICommonSalary, 'aTotalAmount'>,
    Partial<Pick<ICommonSalary, 'aTotalAmount'>> {
  aPeriodAmount?: number;
  tPeriodicDetails: IncentivePeriodicDetails[];
}
export interface IncentivePeriodicDetails extends ICommonMonthCount {}
export interface EmsIncentiveDetails
  extends ICommonSalary,
    EType,
    Pick<ICommonMonthCount, 'aAmount'> {
  dDate: Date;
}

export interface EmsAdvanceRequest extends ICommonSalary {
  eStatus: EmsAdvanceStatus | string;
  eDeductType?: EmsDeductType | string;
  monthlyDetails: MonthlyDetails[];
  tHrApprovedBy?: EmsUser | string;
  tAccApprovedBy?: EmsUser | string;
  dApproveDate?: Date;
  dDisbursementDate?: Date;
  sReason?: string;
  sReply?: string;
  tAttachments?: ICommonFile[];
}

export interface MonthlyDetails extends Partial<ICommonMonthCount> {
  IsDeducted?: boolean;
}
