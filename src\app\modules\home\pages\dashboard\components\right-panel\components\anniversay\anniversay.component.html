<div>
    <p-carousel [circular]="true"  [autoplayInterval]="3000" [value]="todaysAnnivarsaries()" indicatorStyleClass="custom-indicator" showNavigators="false" [numVisible]="1">
        <ng-template let-birthday #item>
            <div class="flex text-slate-700 gap-4 justify-center items-center py-4 shadow-md rounded-md bg-gradient-to-r from-white to-[var(--p-primary-400)]">
                <img [src]="birthday.user.image" alt="" height="78" width="78" class="rounded-full borde">
                <div class="w-[60%] text-center">
                    <p class="text-[30px] cursive-font">Congratulations</p>
                    <p class="text-[20px] flex gap-4 justify-center font-bold name-font"> <span>{{birthday.user.firstName}}</span> <span>{{birthday.user.lastName}}</span>  </p>
                    <p class="text-[10px]">Congratulations on your anniversary!</p>
                    <p class="text-[10px]">Looking forward to many more years together.</p>
                </div>
            </div>
        </ng-template>
    </p-carousel>
    <div>
        <h1 class="h-[20px] mb-10 text-[20px] font-semibold">Upcoming Birthdays</h1>
        <div class="flex flex-wrap gap-12">
            <div *ngFor="let birthday of upcomingAnnivarsaries()">
                <div class="relative">
                    <img [src]="birthday.user.image" alt="" class="h-12 w-12 rounded-full object-cover ">
                    <p> {{birthday.user.firstName}} </p>
                    <div
                        class="absolute -top-2 -right-6 bg-[var(--p-primary-500)] text-white p-[1px] px-1 rounded-lg text-[10px]">
                        {{birthday.anniversaryDate | relativeDate}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>