import { Pipe, PipeTransform } from '@angular/core';
import { TagSeverity } from '@shared/models/types/tag-serverity';
import { TicketPriority } from '../models/ticket';

@Pipe({
  name: 'ticketPriorityColor'
})
export class TicketPriorityColorPipe implements PipeTransform {

  transform(status: TicketPriority, ...args: unknown[]): TagSeverity {
    switch (status) {
      case TicketPriority.HIGH_PRIORITY:
        return 'danger';
      case TicketPriority.MEDIUM_PRIORITY:
        return 'warn';
      case TicketPriority.LOW_PRIORITY:
        return 'info';
      default:
        return 'secondary';
    }
  }

}
