<div class="min-h-screen">
  <!-- Top Navigation Bar -->
  <div class="flex items-center justify-between mb-8">
    <div class="flex bg-transparent">
      <p-tabMenu
        class="custom-tab-menu shadow-md"
        [model]="[
          { label: 'SUMMARY' },
          { label: 'MY PAY' },
          { label: 'MANAGE TAX' }
        ]"
        [activeItem]="{ label: 'SUMMARY' }"
      ></p-tabMenu>
    </div>
    <button
      pButton
      type="button"
      label="View Payslip"
      icon="pi pi-eye"
      class="px-6 py-2 text-white rounded-full shadow-md hover:bg-[#36aaaa]"
      style="font-size: 1rem"
    ></button>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="flex flex-col gap-4">
      <p-card class="shadow-md rounded-2xl bg-white">
        <div
          class="flex flex-row items-center justify-between text-xs text-[#3b7180] font-medium"
        >
          <div class="flex-1 border-[#dbebef] px-2 text-center">
            <div class="mb-1 uppercase text-[13px]">Last Processed Cycle</div>
            <div class="text-base font-semibold text-[#418083] scale-75">
              Jan 2025 (01 Jan - 31 Jan)
            </div>
          </div>
          <div class="flex-1 border-[#dbebef] px-2 text-center">
            <div class="mb-1 uppercase text-[13px]">Working Days</div>
            <div class="text-base font-semibold text-[#418083]">22</div>
          </div>
          <div class="flex-1 px-2 text-center">
            <div class="mb-1 uppercase text-[13px]">Loss of Pay</div>
            <div class="text-base font-semibold text-[#418083]">0</div>
          </div>
        </div>
      </p-card>
      <!-- Payment Information Card -->
      <app-info-card [data]="paymentInfo"></app-info-card>
      <!-- PF Account Info Card -->
      <app-info-card [data]="pfAccountInfo"></app-info-card>
    </div>

    <!-- Identity Information Card -->
    <p-card class="shadow-md rounded-2xl bg-white h-full identity">
      <div class="">
        <div class="text-[#418083] text-[15px] font-semibold mb-6">
          Identity Information
        </div>
        <!-- PAN Section -->
        <app-identity-info [data]="panCardInfo"></app-identity-info>
      </div>
    </p-card>
    <!-- PT Account Info Card -->
    <app-info-card [data]="ptAccount"></app-info-card>
    <!-- Address Proof Card -->
    <p-card class="shadow-md rounded-2xl bg-white">
      <div class="text-[#418083] text-[15px] font-semibold mb-2">
        Address Proof
      </div>
      <app-identity-info [data]="addressProofInfo"></app-identity-info>
    </p-card>
  </div>
</div>
