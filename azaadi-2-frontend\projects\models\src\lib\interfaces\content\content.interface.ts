import {
  ICommonAccess,
  ICommonIdUser,
  ICommonTag,
  ICommonTileValue,
} from '../common.interface';

//   tUser?: string | EmsUser; => tIdUser: string | EmsUser;

export interface EmsContent
  extends Omit<ICommonTag, 'sName'>,
    ICommonIdUser,
    Pick<ICommonAccess, 'bIsActive'>,
    Partial<Pick<ICommonTileValue, 'sTitle'>> {
  sBgUrl?: string;
  sLogo?: string;
  sContent?: string;
  bIsPublish?: boolean;
  bUseForInternal?: boolean;
}
