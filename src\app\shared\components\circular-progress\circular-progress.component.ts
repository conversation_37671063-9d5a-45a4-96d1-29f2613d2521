import { CommonModule } from '@angular/common';
import { Component, computed, input } from '@angular/core';

@Component({
  selector: 'app-circular-progress',
  imports: [CommonModule],
  templateUrl: './circular-progress.component.html',
})
export class CircularProgressComponent {
  label = input.required();
  subLabel = input<string>('');
  size = input<number>(28);
  progress = input<number>(45);
  offsetPrgress = computed(() => 100 - this.progress());
  thickness = input<number>(2);
  color = input<string>('text-cyan-600');
}
