import { Routes } from '@angular/router';
import { authGuard } from '@shared/guards/auth.guard';
import { guestGuard } from '@shared/guards/guest.guard';

export const routes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./modules/modules.routes').then((m) => m.MODULE_ROUTES),
    canActivate: [authGuard],
  },
  {
    path: 'login',
    loadComponent: () =>
      import('./modules/login/login.component').then((m) => m.LoginComponent),
    canActivate: [guestGuard],
  },
];
