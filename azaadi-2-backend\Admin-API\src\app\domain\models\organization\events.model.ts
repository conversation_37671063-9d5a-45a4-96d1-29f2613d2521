import { model, Model, models, Schema } from "mongoose";
import { IEvent } from "../../interfaces/organization/calendar.interface";

/**
 * Event model class that defines the schema and provides access to the Event collection.
 * 
 * @class Event
 * @description Handles the schema definition and model creation for Events
 */
export default class Event {
    /**
     * Mongoose schema definition for Event
     * @private
     */
    private static _schema = new Schema<IEvent>(
        {
            sName: {
                type: String,
                required: [true, 'Name is missing!'],
                trim: true,
                index: true // Indexed for faster search and filtering by event name
            },
            sTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by event tag
            },
            sDescription: {
                type: String,
                trim: true
            },
            sUrl: {
                type: String,
                trim: true
            },
            dStartDate: {
                type: Date,
                required: [true, "Have to define start Date of Event"]
            },
            dEndDate: {
                type: Date,
                trim: true
            },
            bEveryYear: {
                type: Boolean,
                default: false
            },
            bIsFullDay: {
                type: Boolean,
                default: false
            },
            tCalenderTemplate: {
                type: Schema.Types.ObjectId,
                ref: "calendertemplate",
                trim: true,
                required: [true, "Provide calender template"],
                index: true // Indexed for faster lookups and joins by calendar template
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for Event
     * @private
     */
    private static _model: Model<IEvent> = models?.Event || model<IEvent>("event", Event._schema);

    /**
     * Get the Mongoose model for Event
     * @returns {Model<IEvent>} The Event model
     */
    public static get model(): Model<IEvent> {
        return Event._model;
    }
}