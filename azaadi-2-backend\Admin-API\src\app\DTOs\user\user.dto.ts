import { Types } from "mongoose";
import { IOrganizationInfoDTO } from "../organization/organization.dto";
import { IRoleInfoDTO } from "../organization/role.dto";
import { IDepartmentInfoDTO, IDesignationInfoDTO } from "../organization/department.dto";
import { IShiftInfoDTO } from "../organization/shift.dto";
import { IUserManageAccessDTO } from "../organization/manageAccess.dto";

export interface IUserProfileDTO {
    _id: string | Types.ObjectId;
    tIdEmployee: string | Types.ObjectId;
    sEmail: string;
    sPassword: string;
    aPhoneNumber: number;
    sProfileUrl?: string;
    sWorkingType?: string;
    tOrganizations: IOrganizationInfoDTO[];
    tRole: IRoleInfoDTO;
    tDepartment: IDepartmentInfoDTO;
    tDesignation: IDesignationInfoDTO;
    tUserDetails: IUserProfileDetailsDTO;
    tShift?: IShiftInfoDTO[];
    bIsActive: boolean;
    bCanLogin: boolean;
    bOnlyOfficePunch: boolean;
    bIsResigned: boolean;
    bIsCreatedBySuperAdmin: boolean;
    bIsPermanentWFH: boolean;
}

export interface IUserProfileDetailsDTO {
    _id: string | Types.ObjectId;
    sName: string;
    bIsActive: boolean;
}

export interface IUserAuthCredentialsDTO {
    sEmail: string;
    sPassword: string;
}

export interface IUserAuthenticateResponseDTO {
    tUser: Omit<IUserProfileDTO, 'sPassword'>;
    sAccessToken: string;
    tUserAccess: IUserManageAccessDTO[];
}

export interface IEncryptedUserInfoDTO {
    sEmail: string;
    tOrganizations: string[];
    tRole: string;
    tDepartment: string;
    tDesignation: string;
    tShifts?: string[];
    bIsActive: boolean;
    bCanLogin: boolean;
    bOnlyOfficePunch: boolean;
    bIsCreatedBySuperAdmin: boolean;
    bIsPermanentWFH: boolean;
}