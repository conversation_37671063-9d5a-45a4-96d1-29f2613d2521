import { IRoleSettings } from "../../interfaces/organization/role.interface";
import { RoleSettings } from "../../models";
import Repository from "../repository";
import IRoleSettingsRepository from "./abstracts/roleSettingsRepository.abstract";

export default class RoleSettingsRepository extends Repository<IRoleSettings> implements IRoleSettingsRepository {
    constructor() {
        super(RoleSettings.model);
    }
}