import mongoose from "mongoose";
import { createTunnel } from 'tunnel-ssh';
import { LogUtils } from "../utils/log.utils";
import environment from "./environment.config";
/**
 * A class that handles the configuration and connection to a MongoDB database.
 */
export default class DatabaseConfig {
    /**
     * The connection URI for the MongoDB database.
     * @protected
     */
    protected _connectionURI: string = "";

    /**
     * An instance of the LogUtils class for logging MongoDB-related messages.
     * @protected
     */
    protected _log: LogUtils = new LogUtils("MongoDB");

    /**
     * SSH tunnel configuration for development environment
     * @protected
     */
    protected readonly _tunnelConfig = {
        tunnel: { 
            autoClose: true,
            reconnectOnError: true
        },
        server: { host: '127.0.0.1', port: 27017 },
        forward: {
            srcAddr: '0.0.0.0',
            srcPort: 27017,
            dstAddr: '127.0.0.1',
            dstPort: 27017
        }
    };
    /**
     * Establishes a connection to the MongoDB database.
     *
     * @returns {Promise<void>} A Promise that resolves when the database connection is successful.
     */
    async MongoConnection(): Promise<void> {
        mongoose.set('strictQuery', false);
        mongoose.set('strictPopulate', false);
        
        try {
            this._connectionURI = environment.MONGO_DB_URL as string;

            if (environment.NODE_ENV === "production") {
                await mongoose.connect(this._connectionURI);
                this._log.success("Database connected successfully");
            } else {
                const sshConfig = {
                    host: environment.SSH_HOST as string,
                    port: Number(environment.SSH_PORT),
                    username: environment.SSH_USERNAME as string,
                    password: environment.SSH_PASSWORD as string
                };

                const tunnelConfig = {
                    autoClose: true,
                    reconnectOnError: true
                };

                const serverConfig = {
                    host: '127.0.0.1',
                    port: 27017
                };

                const forwardConfig = {
                    srcAddr: '0.0.0.0',
                    srcPort: 27017,
                    dstAddr: '127.0.0.1',
                    dstPort: 27017
                };

                const [server, conn] = await createTunnel(tunnelConfig, serverConfig, sshConfig, forwardConfig);

                server.on('error', (error) => {
                    this._log.error(`SSH tunnel server error: ${error}`);
                });

                conn.on('error', (error) => {
                    this._log.error(`SSH tunnel connection error: ${error}`);
                });

                await mongoose.connect(this._connectionURI);
                this._log.success("Database connected through SSH tunnel");
            }
        } catch (error: any) {
            this._log.error(`Unable to establish connection!\n${error.message || error}`);
            throw error;
        }
    }
}