import { IUserDetails } from "../../interfaces/user/user.interface";
import { UserDetails } from "../../models";
import Repository from "../repository";
import IUserDetailsRepository from "./abstract/userDetailsRepository.abstract";

export default class UserDetailsRepository extends Repository<IUserDetails> implements IUserDetailsRepository {
    constructor() {
        super(UserDetails.model);
    }
}