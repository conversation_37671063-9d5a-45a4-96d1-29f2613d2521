import { inject, Injectable } from '@angular/core';
import { LeaveBalanceApiService } from './leavebalance.api.service';
import { Observable } from 'rxjs';
import { ILeaveBalance } from '../../models/leave';

@Injectable()
export class LeaveBalanceService {
  private readonly _leaveBalanceApiService = inject(LeaveBalanceApiService);

  fetchLeaveBalance(): Observable<ILeaveBalance[]> {
    return this._leaveBalanceApiService.fetchRequestData();
  }
}
