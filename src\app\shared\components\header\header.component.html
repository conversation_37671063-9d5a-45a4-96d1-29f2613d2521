<nav class="h-20 flex justify-between bg-[var(--p-content-background)] py-5">
  <div class="flex items-center">
    <img
      class="h-14 w-14 ms-8 object-cover rounded-full cursor-pointer"
      src="assets/images/logo.png"
      alt="logo"
    />
  </div>



  <div class="flex items-center">
    
    <p-button
      [rounded]="true"
      label="My Tickets"
      severity="secondary"
      (click)="isTicketDrawerVisible = !isTicketDrawerVisible"
    />
    <p-divider layout="vertical" class="mx-5 py-5" />
    <div (click)="toggleDarkMode()">
      <p-button
        [rounded]="true"
        [icon]="isDarkMode ? 'pi pi-sun' : 'pi pi-moon'"
        severity="secondary"
      />
    </div>

    <p-divider layout="vertical" class="mx-5 py-5" />

    <p-button
      [rounded]="true"
      label="{{ selectedLanguage().name }}"
      severity="secondary"
      (click)="lang.toggle($event)"
    >
      <span class="pi pi-language"></span>
    </p-button>

    <p-menu
      #lang
      [popup]="true"
      [model]="languageItems"
      [style]="{ padding: '.5rem' }"
    >
      <ng-template let-item pTemplate="item">
        <div class="flex items-center">
          <img
            [src]="getFlag(item.label)"
            class="w-6 h-6 rounded-full object-cover border border-black"
            alt=""
          />
          <div
            class="p-1 rounded-md flex gap-3 items-center cursor-pointer ml-2"
          >
            {{ item.label | translate }}
          </div>
        </div>
      </ng-template>
    </p-menu>

    <p-divider layout="vertical" class="mx-5 py-5" />

    <p-button
      rounded="true"
      label="{{ organization | translate }}"
      severity="secondary"
      (click)="organizationMenu.toggle($event)"
    />
    <p-menu
      #organizationMenu
      [model]="organizationItems"
      [popup]="true"
      [style]="{ padding: '.5rem' }"
    >
      <ng-template let-item pTemplate="item">
        <div
          (click)="selectOrganization(item.label)"
          class="p-1 rounded-md flex gap-3 items-center cursor-pointer"
        >
          <span [ngClass]="item.icon"></span>
          {{ item.label | translate }}
        </div>
      </ng-template>
    </p-menu>

    <p-divider layout="vertical" class="mx-5 py-5" />

    <div
      role="button"
      (click)="profile.toggle($event)"
      class="cursor-pointer flex items-center gap-2 me-5"
    >
      <img
        class="h-12 w-12 object-cover rounded-full"
        src="assets/images/profile.jpg"
        alt=""
      />
      <div class="grid grid-row-2 items-center">
        <p class="font-bold">Rizwan Haque</p>
        <p>Manager</p>
      </div>
      <i class="pi pi-angle-down" style="font-size: 1rem"></i>

      <p-menu
        #profile
        [model]="profileItems"
        [popup]="true"
        [style]="{ padding: '1rem', minWidth: '15rem', marginRight: '1rem' }"
      >
        <ng-template let-item pTemplate="item">
          <a class="p-1 px-2 rounded-md flex gap-3 items-center">
            <span [ngClass]="item.icon"></span>
            {{ item.label | translate }}
          </a>
        </ng-template>
      </p-menu>
    </div>
  </div>
</nav>

<p-drawer styleClass="!w-[75vw]" header="Recent Tickets" [(visible)]="isTicketDrawerVisible" position="right">
  <app-ticket/>
</p-drawer>
