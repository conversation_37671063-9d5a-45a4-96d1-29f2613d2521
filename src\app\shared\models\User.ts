import { IEmployeeId } from './EmployeeId';
import { IRole } from './Role';
import { IShift } from './Shift';
import { IUserDetails } from './UserDetails';

export interface IUser {
  id?: number;
  avatar?: string;
  image?: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  email?: string;
  address?: {
    street: string;
    suite: string;
    city: string;
    zipcode: string;
  };
  phone?: string;
}

export interface IUserData {
  _id: string;
  sPassword: string;
  sEmail: string;
  aPhoneNumber: number;
  tBranches: any[];
  tRole: IRole;
  bIsActive: boolean;
  bCanLogin: boolean;
  bOnlyOfficePunch: boolean;
  tShift: IShift[];
  bIsApplicableAdvance: boolean;
  bIsResigned: boolean;
  bIsCreatedBySuperAdmin: boolean;
  bIsPermanentWFH: boolean;
  tIdEmployee: IEmployeeId;
  tUserDetails: IUserDetails;
}
