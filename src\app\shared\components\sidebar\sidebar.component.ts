import {
  Component,
  effect,
  ElementRef,
  HostListener,
  inject,
  OnInit,
  QueryList,
  ViewChildren,
} from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { ToastModule } from 'primeng/toast';
import { CommonModule } from '@angular/common';
import { PopoverModule } from 'primeng/popover';
import { CardModule } from 'primeng/card';
import { PanelModule } from 'primeng/panel';
import menuItemData from '../../../../assets/menu.json';
import SidebarMenuItem from '../../models/SidebarMenuItem';
import { TranslateService } from '@ngx-translate/core';
import { LanguageService } from '../../services/language/language.service';

@Component({
  selector: 'app-sidebar',
  imports: [
    RouterModule,
    TooltipModule,
    ButtonModule,
    MenuModule,
    ToastModule,
    CommonModule,
    PopoverModule,
    CardModule,
    PanelModule,
  ],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
})
/**
 * `SidebarComponent` manages the sidebar navigation, including menu visibility,
 * active menu tracking, and handling user interactions like clicks and hovers.
 */
export class SidebarComponent implements OnInit {
  /**
   * References to sidebar elements for detecting clicks inside the sidebar.
   */
  @ViewChildren('sidebarRef') sidebarRefs!: QueryList<ElementRef>;

  /**
   * References to menu elements for handling visibility toggling.
   */
  @ViewChildren('menuRef', { read: ElementRef })
  menuRefs!: QueryList<ElementRef>;

  /** Injected language service instance */
  languageService = inject(LanguageService);

  /** Index of the last opened menu. */
  lastMenuIndex: number = -1;

  /** Indicates whether any menu is currently active. */
  isMenuActive: boolean = false;

  /** Index of the currently active menu based on the current route. */
  activeMenuIndex: number = 0;

  activeSubmenuIndex: number = 0;

  /** List of menu items to be displayed in the sidebar. */
  menuItems: SidebarMenuItem[] = [];

  /** Injected Angular Router instance to track navigation changes. */
  router = inject(Router);

  /** List of menu items filtered based on user role or preferences. */
  filteredMenuItems: SidebarMenuItem[] = [];

  /** Injected translation service instance */
  translateService = inject(TranslateService);

  /**
   * Constructor initializes menu translation effect.
   */
  public constructor() {
    effect(() => {
      this._initializeMenu();
    });
  }

  /**
   * Initializes the sidebar by setting up menu items and subscribing to router events.
   */
  ngOnInit(): void {
    this._initializeMenu();
  }

  /**
   * Handles document-wide click events to close the menu if the user clicks outside the sidebar.
   *
   * @param event The mouse event containing click information.
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedInsideSidebar = this.sidebarRefs.some((ref) =>
      ref.nativeElement.contains(event.target)
    );
    if (!clickedInsideSidebar) {
      this.closeMenu();
    }
  }

  /**
   * Toggles menu visibility based on the provided index.
   *
   * @param index The index of the menu to toggle.
   */
  toggleMenuVisibility(index: number) {
    if (this.lastMenuIndex === index) {
      this.closeMenu();
    } else {
      this.showMenu(index);
    }
  }

  /**
   * Closes the currently active menu.
   */
  closeMenu() {
    const menu = this.menuRefs.toArray()[this.lastMenuIndex];
    if (menu) {
      menu.nativeElement.classList.add('hidden');
    }
    this.lastMenuIndex = -1;
    this.isMenuActive = false;
  }

  navigateTo(activeMenuIndex: number) {
    this.activeMenuIndex = activeMenuIndex;
  }

  /**
   * Displays the menu corresponding to the given index.
   * Adjusts positioning if the menu overflows the screen height.
   *
   * @param index The index of the menu to show.
   */
  showMenu(index: number) {
    const screenHeight = window.innerHeight;
    const sideBarElement = this.sidebarRefs.toArray()[index].nativeElement;
    const sideBarElementRect = sideBarElement.getBoundingClientRect();
    const menuElement = this.menuRefs.toArray()[index].nativeElement;

    menuElement.classList.remove('hidden');
    menuElement.style.top = sideBarElementRect.top + 'px';
    this.isMenuActive = true;

    const prevMenu = this.menuRefs.toArray()[this.lastMenuIndex];

    if (prevMenu && this.lastMenuIndex !== index) {
      prevMenu.nativeElement.classList.add('hidden');
    }
    this.lastMenuIndex = index;

    if (menuElement) {
      const rect = menuElement.getBoundingClientRect();
      if (rect.bottom > screenHeight) {
        menuElement.style.top = screenHeight - rect.height + 'px';
      }
    }
  }

  /**
   * Displays the menu corresponding to the given index if a menu is already active.
   * Used when the user hovers over a menu item while a menu is already visible.
   *
   * @param index The index of the menu to show.
   */
  showMenuOnHover(index: number) {
    if (this.isMenuActive) {
      this.showMenu(index);
    }
  }

  /**
   * Initializes menu items from predefined data and applies translations.
   */
  private _initializeMenu() {
    this.menuItems = menuItemData.map(SidebarMenuItem.fromJSON);
    this._translateMenu();
  }

  /**
   * Translates menu items based on the selected language.
   */
  private _translateMenu() {
    const lang = this.languageService.selectedLanguage().code;
    this.translateService.use(lang);
    this.filteredMenuItems = this.menuItems.map((menu) =>
      this._translateMenuItem(menu)
    );
  }

  /**
   * Recursively translates menu items.
   * @param menu The menu item to translate.
   */
  private _translateMenuItem(menu: SidebarMenuItem): SidebarMenuItem {
    const modifiedKey = this._modifyLabel(menu.label);
    this.translateService.get(modifiedKey).subscribe((translatedLabel) => {
      menu.label = translatedLabel;
      if (menu.children) {
        menu.children = menu.children.map((child) =>
          this._translateMenuItem(child)
        );
      }
    });
    return menu;
  }

  /**
   * Modifies a label for translation lookup.
   * @param str The label to modify.
   */
  private _modifyLabel(str: string): string {
    return str.replace(/\s+/g, '_');
  }
}
