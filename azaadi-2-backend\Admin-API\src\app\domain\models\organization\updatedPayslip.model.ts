import { model, Model, models, Schema } from "mongoose";
import { IUpdatedPayslip } from "../../interfaces/organization/payslip.interface";
import { EMSUpdatedSalaryStructureCategory } from "../../../../utils/meta/enum.utils";
import { ISalaryStructureTemplate } from "../../interfaces/organization/salaryStructure.interface";
import { PayslipSchemas } from "../shared/payslip-schemas";

/**
 * UpdatedPayslip model class that defines the schema and provides access to the UpdatedPayslip collection.
 * 
 * @class UpdatedPayslip
 * @description Handles the schema definition and model creation for Updated Payslips
 */
export default class UpdatedPayslip {
    /**
     * Mongoose schema definition for SalaryStructureTemplate
     * @private
     */
    private static _salaryStructureTemplateSchema = new Schema<ISalaryStructureTemplate>({
        tSalaryStructure: {
            type: Schema.Types.Mixed
        },
        eType: {
            type: String,
            enum: EMSUpdatedSalaryStructureCategory
        }
    }, { _id: false });

    /**
     * Mongoose schema definition for UpdatedPayslip
     * @private
     */
    private static _schema = new Schema<IUpdatedPayslip>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                required: [true, "User ID is required"],
                trim: true,
                ref: 'login',
                index: true // Indexed for faster lookups by user
            },
            aMonth: {
                type: Number,
                required: [true, "Month is required"],
                min: [1, "Month must be between 1 and 12"],
                max: [12, "Month must be between 1 and 12"],
                trim: true,
                index: true // Indexed for filtering by month
            },
            aYear: {
                type: Number,
                required: [true, "Year is required"],
                min: [1900, "Invalid year"],
                max: [9999, "Invalid year"],
                trim: true,
                index: true // Indexed for filtering by year
            },
            aSalaryDay: {
                type: Number,
                trim: true,
                min: [1, "Salary day must be between 1 and 31"],
                max: [31, "Salary day must be between 1 and 31"]
            },
            dStartDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            dEndDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            tSalaryStructure: {
                type: Schema.Types.Mixed
            },
            eType: {
                type: String,
                enum: EMSUpdatedSalaryStructureCategory,
                index: true // Indexed for filtering by salary structure type
            },
            bIsReportingAuthApproved: {
                type: Boolean,
                default: false,
                index: true // Indexed for filtering by approval status
            },
            bIsHrApproved: {
                type: Boolean,
                default: false,
                index: true // Indexed for filtering by HR approval status
            },
            aMiscAmount: {
                type: Number,
                default: 0
            },
            aMiscDescription: {
                type: String,
                trim: true
            },
            user: {
                type: PayslipSchemas.userSchema
            },
            userBank: {
                type: PayslipSchemas.userBankSchema
            },
            leaveBalance: {
                type: [PayslipSchemas.leaveBalanceSchema],
                default: []
            },
            salaryStructureTemplate: {
                type: UpdatedPayslip._salaryStructureTemplateSchema
            }
        },
        PayslipSchemas.basePayslipConfig
    );

    /**
     * Mongoose model for UpdatedPayslip
     * @private
     */
    private static _model: Model<IUpdatedPayslip> = models?.UpdatedPayslip || model<IUpdatedPayslip>("updatedPayslip", UpdatedPayslip._schema);

    /**
     * Get the Mongoose model for UpdatedPayslip
     * @returns {Model<IUpdatedPayslip>} The UpdatedPayslip model
     */
    public static get model(): Model<IUpdatedPayslip> {
        return UpdatedPayslip._model;
    }
}