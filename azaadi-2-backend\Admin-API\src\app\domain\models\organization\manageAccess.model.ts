import { model, Model, models, Schema } from "mongoose";
import { IManageAccess } from "../../interfaces/organization/manageAccess.interface";

/**
 * ManageAccess model class that defines the schema and provides access to the ManageAccess collection.
 * 
 * @class ManageAccess
 * @description Handles the schema definition and model creation for Access Management
 */
export default class ManageAccess {
    /**
     * Mongoose schema definition for ManageAccess
     * @private
     */
    private static _schema = new Schema<IManageAccess>(
        {
            sName: {
                type: String,
                required: [true, "Name is missing!"],
                trim: true,
                index: true // Indexed for faster search and filtering by access name
            },
            sTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by access tag
            },
            tRole: {
                type: Schema.Types.ObjectId,
                ref: "role",
                required: [true, "Role is required"],
                trim: true,
                index: true // Indexed for faster lookups and joins by role
            },
            bCanView: {
                type: Boolean,
                default: true
            },
            bCanWrite: {
                type: Boolean,
                default: false
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for ManageAccess
     * @private
     */
    private static _model: Model<IManageAccess> = models?.usermanageaccess || model<IManageAccess>("usermanageaccess", ManageAccess._schema);

    /**
     * Get the Mongoose model for ManageAccess
     * @returns {Model<IManageAccess>} The ManageAccess model
     */
    public static get model(): Model<IManageAccess> {
        return ManageAccess._model;
    }
}