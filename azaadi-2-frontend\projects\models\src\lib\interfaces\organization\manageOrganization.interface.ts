import {
    EmsFileType,
    EmsLeaveType,
    EmsAnnouncementType,
} from '../../enums/managements.enum';
import {
    ICommon,
    ICommonDescription,
    ICommonName,
    ICommonOrg,
    ICommonRole,
    ICommonStartEndDate,
    ICommonTag,
    ICommonTileValue,
    ICommonWithIdUser,
} from '../common.interface';

export interface EmsBranches extends ICommonTag, ICommonOrg { }

export interface EmsDepartments extends EmsBranches {
    tDepartmentHead: string | EmsRole;
}

export interface EmsDesignation extends EmsBranches { }

export interface EmsManageAccess extends ICommonTag, ICommonRole {
    bCanView: boolean;
    bCanWrite: boolean;
}
export interface EmsManageAssets extends ICommonTag { }
export interface EmsManageDocuments extends EmsBranches {
    eType: EmsFileType[];
    bAllowMultiple?: boolean;
}

export interface EmsManageLeave extends ICommonWithIdUser, ICommonDescription {
    eType: EmsLeaveType;
    aCount: number;
}

export interface EmsCalendarEvent extends ICommonTag, ICommonDescription {
    sUrl?: string;
    dStartDate: Date;
    dEndDate?: Date;
    bEveryYear?: boolean;
    tCalenderTemplate: EmsCalendarTemplate;
}

export interface EmsManageLogs extends ICommonWithIdUser {
    sMetaKey: string[];
    sMetaValue: string[];
}

export interface EmsOrganization extends ICommonTag { }

export interface EmsOrganizationSetting
    extends ICommon,
    ICommonOrg,
    ICommonTileValue {
    sOrgTag: string;
}

export interface EmsRole extends ICommonTag, ICommonOrg {
    tHead: EmsRole | string;
    tHrReport: EmsRole | string;
    tAdditionalOrg: EmsOrganization[] | string[];
}

export interface EmsRoleSettings
    extends ICommon,
    ICommonRole,
    ICommonTileValue {
    tManagedBy?: EmsRole | string;
}

export interface EmsTree extends ICommonWithIdUser {
    tParentNode?: string | EmsTree;
    tChildNode: string[] | EmsTree[];
    tDepartment?: string | EmsDepartments;
    bIsHead?: boolean;
    sColor?: string;
}

export interface EmsBiometric extends ICommon {
    txnId: string;
    dvcId: string;
    dvcIP: string;
    txnDateTime: string;
    punchId: string;
    mode: string;
}

export interface EmsCalendarTemplate extends ICommonTag, ICommonOrg { }

export interface EmsManageRoleRequest
    extends ICommon,
    ICommonRole,
    ICommonTileValue {
    tApprovedBy: EmsRole | string;
    aCount: number;
}

export interface EmsManageUserRequest
    extends ICommonWithIdUser,
    ICommonRole,
    ICommonDescription {
    tType: EmsManageRoleRequest | string;
    aCount: number;
}
export interface EmsAnnouncement
    extends ICommonName,
    ICommonOrg,
    ICommonStartEndDate,
    EmsAnnouncementGeneric { }

export interface EmsAnnouncementGeneric extends ICommonDescription {
    sUrl?: string;
    eType?: EmsAnnouncementType;
}
export interface EmsPrivacyPolicy extends ICommonTag, ICommonRole, ICommonOrg {
    sContent?: string;
}
export interface EmsHrPolicy extends EmsPrivacyPolicy { }

export interface EmsShift extends ICommonTag, ICommonOrg {
    sTimezone?: string;
    sTime?: string;
    isDefault?: boolean;
    sPunchInTime?: string;
    sPunchOutTime?: string;
    sTimeBuffer?: string; // In hours
}
export interface EmsMailTemplate extends ICommonTag, ICommonOrg {
    sBody?: string;
}
