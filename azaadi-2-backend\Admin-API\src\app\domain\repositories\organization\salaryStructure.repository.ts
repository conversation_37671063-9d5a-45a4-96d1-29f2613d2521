import { ISalaryStructure } from "../../interfaces/organization/salaryStructure.interface";
import { SalaryStructure } from "../../models";
import Repository from "../repository";
import ISalaryStructureRepository from "./abstracts/salaryStructureRepository.abstruct";

export default class SalaryStructureRepository extends Repository<ISalaryStructure> implements ISalaryStructureRepository {
    constructor() {
        super(SalaryStructure.model);
    }
}