import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TeamAvailabilityApiService } from './teamavailability.api.service';

@Injectable()
export class TeamAvailabilityService {
  private readonly _teamAvailabilityApiService = inject(
    TeamAvailabilityApiService
  );

  fetchRequestData(): Observable<any> {
    return this._teamAvailabilityApiService.fetchTeamAvailabilityData();
  }
}
