import { Types } from "mongoose";
import { IConcept, IIdConcept } from "../concept.interface";
import { IUser } from "./user.interface";
import { IOrganization } from "../organization/organization.interface";
import { IManageDocument } from "../organization/manageDocument.interface";
import { EMSFileType } from "../../../../utils/meta/enum.utils";

export interface IUserBankDetails extends IConcept {
    tIdUser: string | Types.ObjectId | IUser;
    sBankName?: string;
    sBankAccount?: string;
    sIfscCode?: string;
    sBranchName?: string;
    bIsActive: boolean;
    tOrganization?: string | Types.ObjectId | IOrganization;
}

export interface IUserBankInfo extends IIdConcept {
    sBankName?: string;
    sBankAccount?: string;
    sIfscCode?: string;
    sBranchName?: string;
}

export interface IDocumentDetails {
    sName?: string;
    sSize?: string;
    sUrl?: string;
    eType?: EMSFileType;
    bStatus?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface IUserDocuments extends IConcept {
    tIdUser: string | Types.ObjectId | IUser;
    sName: string;
    tManageDoc?: string | Types.ObjectId | IManageDocument;
    tDocumentDetails?: IDocumentDetails[];
    tOrganization?: string | Types.ObjectId | IOrganization;
}

