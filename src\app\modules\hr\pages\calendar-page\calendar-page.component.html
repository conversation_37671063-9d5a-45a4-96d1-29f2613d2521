<div class="grid grid-cols-12 gap-4 mb-8 h-64">
    <div class="flex flex-col col-span-7 h-full">
        <h6 class="p-2">Calendar</h6>
        <app-calendar-view class="flex-1"></app-calendar-view>
    </div>
    <div class="flex flex-col col-span-5 h-full">
        <h6 class="p-2">Leave Balances</h6>
        <p-card>
            <div class="flex gap-4 rounded-xl px-4 justify-around">
                <app-leave-balance-card [data]="leavebalance_data[0]"></app-leave-balance-card>
                <p-divider layout="vertical"></p-divider>
                <app-leave-balance-card [data]="leavebalance_data[1]"></app-leave-balance-card>
                <p-divider layout="vertical"></p-divider>
                <app-leave-balance-card [data]="leavebalance_data[2]"></app-leave-balance-card>
            </div>
        </p-card>
    </div>
</div>
<h6 class="p-2">Logs & Requests</h6>
<div class="flex justify-between items-center mb-4">
    <p-selectbutton [options]="stateOptions" [(ngModel)]="value" optionLabel="label" optionValue="value" aria-labelledby="basic" />
    <div class="flex gap-2 items-center justify-center">
        <p-toggleswitch [(ngModel)]="checked" />
        <div>24 hour format</div>
    </div>
</div>
<app-table></app-table>