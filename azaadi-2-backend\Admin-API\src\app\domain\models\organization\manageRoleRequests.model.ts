import { model, models, Schema } from "mongoose";
import { IManageRoleRequest } from "../../interfaces/organization/manageRequest.interface";

/**
 * MannageRoleRequests model class that defines the schema and provides access to the ManageRoleRequests collection.
 * 
 * @class ManageRoleRequests
 * @description Handles the schema definition and model creation for Role Management Requests
 */
export default class ManageRoleRequests {
    private static _schema: Schema<IManageRoleRequest> = new Schema<IManageRoleRequest>({
        tRole: {
            type: Schema.Types.ObjectId,
            ref: 'role', // Reference to the Role model
            required: [true, 'Role is required'],
            trim: true,
            index: true // Indexed for faster search and filtering by role
        },
        sType: {
            type: String,
            required: [true, 'Type is required'],
            trim: true,
            index: true // Indexed for faster search and filtering by type
        },
        aCount: {
            type: Number,
            default: 0
        },
        tApprovedBy: {
            type: Schema.Types.ObjectId,
            ref: 'role', // Reference to the Role model for approved by
            required: [true, 'Approved by is required'],
            trim: true,
            index: true // Indexed for faster search and filtering by approved by
        }
    }, {
        timestamps: true,
        versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking since we're not using optimistic concurrency control
    });

    /**
     * Mongoose model for ManageRoleRequest
     * @private
     */
    private static _model = models?.manageRoleRequest || model<IManageRoleRequest>("manageRoleRequest", ManageRoleRequests._schema);

    /**
     * Get the Mongoose model for ManageRoleRequest
     * @returns {Model<IManageRoleRequest>} The ManageRoleRequest model
     */
    public static get model() {
        return ManageRoleRequests._model;
    }
}