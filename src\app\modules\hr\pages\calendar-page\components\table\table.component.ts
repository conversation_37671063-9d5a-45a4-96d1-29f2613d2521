import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { TableModule } from 'primeng/table';
import { CheckinTimeComponent } from '../checkin-time/checkin-time.component';

@Component({
  selector: 'app-table',
  imports: [
    CommonModule,
    TableModule,
    DropdownModule,
    FormsModule,
    CheckinTimeComponent,
  ],
  templateUrl: './table.component.html',
  styleUrl: './table.component.scss',
})
export class TableComponent {
  range = 'Last 30 Days';

  attendanceData = [
    {
      date: 'Mar 08, Sat',
      label: 'W-OFF',
      checkin: null,
      duration: null,
      checkout: null,
      overTime: null,
      effectiveHours: null,
      mode: null,
    },
    {
      date: 'Mar 07, Fri',
      checkin: '8:55',
      duration: '9h 12m',
      checkout: '19:20',
      overTime: '+1h 2min',
      effectiveHours: '',
      mode: 'WFO',
    },
    {
      date: 'Mar 06, Thu',
      checkin: '8:50',
      duration: '9h 12m',
      checkout: '19:00',
      overTime: '+1h 2min',
      effectiveHours: '',
      mode: 'WFH',
    },
    {
      date: 'Mar 05, Wed',
      checkin: '8:50',
      duration: '9h 12m',
      checkout: '19:00',
      overTime: '+1h 2min',
      effectiveHours: '',
      mode: 'WFH',
    },
    {
      date: 'Mar 04, Tue',
      checkin: '8:50',
      duration: '9h 12m',
      checkout: '19:00',
      overTime: '+1h 2min',
      effectiveHours: '',
      mode: 'WFH',
    },
  ];
}
