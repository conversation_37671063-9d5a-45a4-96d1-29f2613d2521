import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
} from '@angular/core';
import { Router, RouterModule, RouterOutlet } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { CommonModule } from '@angular/common';
import { MediaMatcher } from '@angular/cdk/layout';
import { provideNativeDateAdapter } from '@angular/material/core';
import { HtmlClassDirective } from './html-class.directive';
import { NavbarComponent, SidebarComponent } from '../../projects/components/src/public-api';
import { Theme, ThemeService } from './theme-service.service';
import { NavItem } from '../../projects/models/src/public-api';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    RouterModule,
    MatSidenavModule,
    HtmlClassDirective,
    SidebarComponent,
    NavbarComponent,
  ],
  providers: [provideNativeDateAdapter()],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  title = 'Azaadi App';
  mobileQuery?: MediaQueryList;
  theme: Theme = Theme.Light;
  private _mobileQueryListener?: () => void;
  menu: NavItem[] = [
    {
      displayName: 'Main',
      isAccess:true
    },
    {
      displayName: 'Dashboard',
      iconName: 'dashboard',
      route: 'dashboard',
      isAccess:true
    },
    {
      displayName: 'Employee',
      isAccess:true,
    },
    {
      displayName: 'Employees',
      iconName: 'people',
      route: 'employee',
      isAccess:true,
    },
    {
      displayName: 'Attendance',
      iconName: 'poll',
      route: 'attendance',
      isAccess:true,
    },
    {
      displayName: 'Requests',
      iconName: 'view_list',
      route: 'request',
      isAccess:true,
    }
  ];
  activeUrl = window.location.pathname.split('/')[1];
  constructor(
    changeDetectorRef: ChangeDetectorRef,
    media: MediaMatcher,
    private themeService: ThemeService,
    private router: Router 
  ) {
    this.mobileQuery = media.matchMedia('(max-width: 700px)');
    this._mobileQueryListener = () => changeDetectorRef.detectChanges();
    this.mobileQuery.addListener(this._mobileQueryListener);
    this.themeService.theme$.subscribe((theme) => (this.theme = theme));
  }
  onMenuClick(route:string){
    this.router.navigate([route]);
    this.activeUrl = route;
    
  }
}
