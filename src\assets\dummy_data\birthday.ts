import { IBirthday } from 'src/app/modules/home/<USER>/dashboard/models/birthday';

export const upcomingBirthdays: IBirthday[] = [
  {
    user: {
      firstName: '<PERSON><PERSON>',
      lastName: '<PERSON>',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    birthdayDate: new Date(new Date().setDate(new Date().getDate() + 1)),
  },
  {
    user: {
      firstName: '<PERSON>ja<PERSON>',
      lastName: 'Verma',
      image: 'https://randomuser.me/api/portraits/women/2.jpg',
    },
    birthdayDate: new Date(new Date().setDate(new Date().getDate() + 2)),
  },
  {
    user: {
      firstName: 'Ravi',
      lastName: '<PERSON>',
      image: 'https://randomuser.me/api/portraits/men/3.jpg',
    },
    birthdayDate: new Date(new Date().setDate(new Date().getDate() + 3)),
  },
  {
    user: {
      firstName: '<PERSON><PERSON><PERSON>',
      lastName: '<PERSON>',
      image: 'https://randomuser.me/api/portraits/women/4.jpg',
    },
    birthdayDate: new Date(new Date().setDate(new Date().getDate() + 4)),
  },
  {
    user: {
      firstName: 'Amit',
      lastName: 'Gupta',
      image: 'https://randomuser.me/api/portraits/men/5.jpg',
    },
    birthdayDate: new Date(new Date().setDate(new Date().getDate() + 5)),
  },
  {
    user: {
      firstName: 'Priya',
      lastName: 'Singh',
      image: 'https://randomuser.me/api/portraits/women/6.jpg',
    },
    birthdayDate: new Date(new Date().setDate(new Date().getDate() + 6)),
  },
];
