import { inject, Injectable } from '@angular/core';
import { RequestApiService } from './request.api.service';
import { map, Observable } from 'rxjs';

@Injectable()
export class RequestService {
  private readonly _requestApiService = inject(RequestApiService);
  fetchRequestData(): Observable<any> {
    return this._requestApiService.fetchRequestData().pipe(
      map((response) => {
        // Process the response as needed
        return response;
      })
    );
  }
}
