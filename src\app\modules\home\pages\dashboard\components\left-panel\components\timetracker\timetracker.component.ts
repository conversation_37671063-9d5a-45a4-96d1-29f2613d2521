import { Component, inject, OnDestroy, OnInit } from '@angular/core';
import { interval, Subscription, take } from 'rxjs';
import { AttendanceService } from '../../../../services/attendance/attendance.service';
import {
  AttendanceType,
  ITodaysAttendance,
} from '../../../../models/Attendance';
import { AttendanceApiService } from '../../../../services/attendance/attendance.api.service';
import { CommonModule } from '@angular/common';
import { TranslatePipe } from '@ngx-translate/core';
import { CircularProgressComponent } from '@shared/components/circular-progress/circular-progress.component';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { SkeletonModule } from 'primeng/skeleton';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'app-timetracker',
  imports: [
    CardModule,
    CommonModule,
    TagModule,
    CircularProgressComponent,
    TranslatePipe,
    SkeletonModule,
    ProgressSpinnerModule,
  ],
  templateUrl: './timetracker.component.html',
  providers: [AttendanceService, AttendanceApiService],
})
export class TimetrackerComponent implements OnInit, OnDestroy {
  timeString = '00:00:00';
  duration = 0; // total duration in seconds
  elapsed = 0; // elapsed time in seconds
  progressDeg = 0; // progress in degrees for UI
  shift = 'Day';
  workingMode = 'WFO';
  totalHours = 0;
  isLoading = false;
  private attendanceService = inject(AttendanceService);
  private timerSub?: Subscription;

  ngOnInit() {
    this.isLoading = true;
    this.attendanceService
      .fetchTimeTrackerData()
      .subscribe((data: ITodaysAttendance) => {
        this.shift = data.tShift;
        this.workingMode =
          data.eAttendanceType === AttendanceType.OFFLINE ? 'WFO' : 'WFH';
        const start = new Date(data.dStartDate).getTime();
        const end = new Date(data.dEndDate).getTime();
        const now = Date.now();
        this.totalHours = Math.floor((end - start) / 3600000);

        // Only track if start date is today
        if (new Date(start).toDateString() !== new Date(now).toDateString())
          return;

        this.duration = Math.floor((end - start) / 1000);

        this.elapsed = Math.floor((now - start) / 1000);
        if (this.elapsed > this.duration) this.elapsed = this.duration;

        this.updateUI();

        // Start timer if time remains
        const remaining = this.duration - this.elapsed;

        if (remaining > 0) {
          this.timerSub = interval(1000)
            .pipe(take(remaining))
            .subscribe(() => {
              this.elapsed++;
              this.updateUI();
            });
        }
        this.isLoading = false;
      });
  }

  ngOnDestroy() {
    this.timerSub?.unsubscribe();
  }

  private updateUI(): void {
    const remaining = Math.max(this.duration - this.elapsed, 0);

    const hours = Math.floor(remaining / 3600);
    const minutes = Math.floor((remaining % 3600) / 60);
    const seconds = remaining % 60;

    this.timeString = [hours, minutes, seconds]
      .map((unit) => unit.toString().padStart(2, '0'))
      .join(':');

    const progress =
      this.duration > 0 ? Math.floor((remaining * 100) / this.duration) : 0;
    this.progressDeg = Math.min(progress, 360);
  }

  formatElapsedTime(): string {
    const hours = Math.floor(this.elapsed / 3600);

    const minutes = Math.floor((this.elapsed % 3600) / 60);

    return `${hours.toString().padStart(2, '0')}h ${minutes
      .toString()
      .padStart(2, '0')}m`;
  }
}
