import { NextFunction, Request, Response } from "express";
import { body, ValidationChain, validationResult } from "express-validator";

/**
 * Class to handle policy-related request validations
 */
export class PoliciesValidator {
    /**
     * Get validation rules for HR policy creation/update
     */
    public static getHRPolicyValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Content validation
            body("sContent")
                .trim()
                .notEmpty()
                .withMessage("Content is required")
                .isLength({ min: 1, max: 50000 })
                .withMessage("Content must not exceed 50000 characters"),

            // Role validation
            body("tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // Organization validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for privacy policy creation/update
     */
    public static getPrivacyPolicyValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Content validation
            body("sContent")
                .trim()
                .notEmpty()
                .withMessage("Content is required")
                .isLength({ min: 1, max: 50000 })
                .withMessage("Content must not exceed 50000 characters"),

            // Role validation
            body("tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // Organization validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for bulk policy operations
     */
    public static getBulkPolicyValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Validate array of policy entries
            body()
                .isArray()
                .withMessage("Request body must be an array of policy entries"),
            
            // Name validation for each entry
            body("*.sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Content validation for each entry
            body("*.sContent")
                .trim()
                .notEmpty()
                .withMessage("Content is required")
                .isLength({ min: 1, max: 50000 })
                .withMessage("Content must not exceed 50000 characters"),

            // Role validation for each entry
            body("*.tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // Organization validation for each entry
            body("*.tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
