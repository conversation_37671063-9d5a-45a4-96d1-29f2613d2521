<div class="grid grid-cols-2 gap-8">
  <div
    *ngFor="let item of holidays"
    [ngClass]="{ 'opacity-40': isHolidayPassed(item.date) }"
  >
    <div class="flex gap-4 items-center">
      <div
        class="rounded-md border-2"
        [ngStyle]="{
          'border-color': item.date | monthColor
        }"
      >
        <p
          class="h-8 w-20 flex items-center justify-center text-xl text-white"
          [ngStyle]="{
              'background-color': item.date | monthColor,
            }"
        >
          {{ item.date | date : "MMM" }}
        </p>
        <p
          class="h-12 w-20 flex items-center justify-center text-2xl font-semibold"
        >
          {{ item.date | date : "dd" }}
        </p>
      </div>
      <div>
        <p class="text-xl font-semibold">
          {{ item.title }}
        </p>
        <p>{{ item.date | date : "EEEE" }}</p>
      </div>
    </div>
  </div>
</div>
