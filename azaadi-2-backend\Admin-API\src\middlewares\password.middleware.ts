import { Schema, Document } from "mongoose";
import bcrypt from "bcrypt";
import environment from "../config/environment.config";
import { ErrorHelper } from "../helpers/error.helper";

/**
 * Class for handling password-related middleware operations
 * 
 * @class PasswordMiddleware
 * @description Handles password hashing and related operations for mongoose schemas
 */
export class PasswordMiddleware {
    private readonly _saltRounds: number;
    private readonly _errorHelper: ErrorHelper;

    constructor() {
        this._saltRounds = +(environment.HASH_SALT || 10);
        this._errorHelper = new ErrorHelper();
    }

    /**
     * Adds password hashing middleware to a mongoose schema
     * @param schema The mongoose schema to add the middleware to
     * @param field The field name that contains the password (defaults to 'sPassword')
     */
    public addHashingMiddleware(schema: Schema, field: string = 'sPassword'): void {
        schema.pre('save', async function(this: Document, next: (err?: Error) => void) {
            if (!this.isModified(field)) return next();
            try {
                const salt = await bcrypt.genSalt(+(environment.HASH_SALT || 10));
                const value = (this as any)[field] as string;
                (this as any)[field] = await bcrypt.hash(value, salt);
                return next();
            } catch (err) {
                return next(err as Error);
            }
        });
    }

    /**
     * Compares a plain text password with a hashed password
     * @param plainText The plain text password
     * @param hashedPassword The hashed password to compare against
     * @returns Promise<boolean> True if passwords match
     */
    public async comparePasswords(plainText: string, hashedPassword: string): Promise<boolean> {
        try {
            return await bcrypt.compare(plainText, hashedPassword);
        } catch (error: any) {
            throw this._errorHelper.customError(error);
        }
    }
}