import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { IOrganization } from './organization.interface';
import { IRole } from './role.interface';

export interface IHRPolicy extends IConcept {
    sName: string;
    sContent: string;
    tRole: string | Types.ObjectId | IRole;
    tOrganization: string | Types.ObjectId | IOrganization;
}

export interface IPrivacyPolicy extends IConcept {
    sName: string;
    sContent: string;
    tRole: string | Types.ObjectId | IRole;
    tOrganization: string | Types.ObjectId | IOrganization;
}