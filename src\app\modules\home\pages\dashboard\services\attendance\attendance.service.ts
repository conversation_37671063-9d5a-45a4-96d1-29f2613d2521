import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { ITodaysAttendance } from '../../models/Attendance';
import { AttendanceApiService } from './attendance.api.service';

@Injectable()
export class AttendanceService {
  private readonly _attendanceApiService = inject(AttendanceApiService);
  fetchTimeTrackerData(): Observable<ITodaysAttendance> {
    return this._attendanceApiService.fetchTimeTrackerData().pipe(
      map((response) => {
        const startDate = new Date(response.dStartDate[0]);
        const endDate = new Date(
          response.dEndDate[response.dEndDate.length - 1]
        );
        return {
          dStartDate: startDate,
          dEndDate: endDate,
          eAttendanceType: response.eAttendanceType,
          eEndAttendanceType: response.eEndAttendanceType,
          tShift: response.tShift,
        };
      })
    );
  }

  fetchAttendanceData(): Observable<ITodaysAttendance[]> {
    return this._attendanceApiService.fetchAttendanceData();
  }
}
