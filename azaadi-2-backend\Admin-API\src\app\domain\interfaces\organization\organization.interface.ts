import { Types } from "mongoose";
import { EMSOrganizationSettingLabel } from "../../../../utils/meta/enum.utils";
import { IConcept, IConceptBase } from "../concept.interface";

export interface IOrganization extends IConcept {
    sName: string;
    sTag: string;
    bIsCreatedBySuperAdmin: boolean;
    bIsActive: boolean;
}

export interface IOrganizationSetting extends IConcept {
    tOrganization: string | Types.ObjectId | IOrganization;
    sOrgTag?: string;
    sTitle: EMSOrganizationSettingLabel;
    sValue: any;
    sType?: string;
}

export interface IBranch extends IConcept {
    sName: string;
    sTag?: string;
    tOrganization?: string | Types.ObjectId | IOrganization;
}

export interface IPartialOrganization extends IConceptBase {
    sTag?: string;
}