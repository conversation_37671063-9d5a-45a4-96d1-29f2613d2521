import { <PERSON><PERSON><PERSON><PERSON>, Router } from "express";
import AuthRoutes from "./auth/auth.route";

/**
 * A class that initializes and manages the routes for an application's operations.
 */
export default class Routes{
    /**
     * The router instance used to define routes.
     * @public
     */
    public routes:IRouter;
    private _authRoutes: AuthRoutes;

    /**
     * Creates an instance of the Routes class and initializes the router and AsyncUtils instances.
     */
    constructor(){
        this.routes=Router();
        this._authRoutes=new AuthRoutes();
        this.initializeRoutes();
    }
    /**
     * Initializes the routes for the application's operations.
     * This method should be overridden to define the actual routes.
     */
    public initializeRoutes():void{
        this.routes.use("/auth", this._authRoutes.routes);
    }
}