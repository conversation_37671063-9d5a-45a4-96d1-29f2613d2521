
/* Datepicker */
body.dark {

  /* //sidebar
  // --mat-sidenav-container-background-color: #2c3038;
  // --mat-expansion-container-background-color: #2c3038;
   //table
  // --mat-table-background-color: #b37aa1;
  // --mat-table-header-headline-color: #c9ad5e;
  // --mat-table-row-item-label-text-color: #4f323f;
  // BUtton
  // --mdc-text-button-label-text-color: #d0d4d8;
  // --mdc-fab-container-color: #2b3035;
  // --mat-fab-foreground-color:#d0d4d8;
  // --mdc-fab-small-container-color: #2b3035;
  // --mat-fab-small-foreground-color:#d0d4d8;
  // //outlined
  // // --mdc-outlined-button-outline-color: #ffffff;
  //  --mdc-outlined-button-label-text-color: #d0d4d8;
  // //protected
  // // --mdc-protected-button-container-color: #0e1012;
  // --mdc-protected-button-label-text-color: #d0d4d8;
  // --mdc-filled-button-container-color:#2b3035;
  // --mdc-filled-button-label-text-color:#d0d4d8; */
  /* default/primary color */
  --mat-datepicker-calendar-date-selected-state-text-color: white;
  --mat-datepicker-calendar-date-selected-state-background-color: #2b3035;
  --mat-datepicker-calendar-date-selected-disabled-state-background-color: #2b3035;
  --mat-datepicker-calendar-date-today-selected-state-outline-color: white;
  --mat-datepicker-calendar-date-focus-state-background-color: #2b3035;
  --mat-datepicker-calendar-date-hover-state-background-color:  #2b3035;
  --mat-datepicker-toggle-active-state-icon-color: #2b3035;
  --mat-datepicker-calendar-date-in-range-state-background-color: rgba(156, 39, 176, .2);
  --mat-datepicker-calendar-date-in-comparison-range-state-background-color: rgba(249, 171, 0, .2);
  --mat-datepicker-calendar-date-in-overlap-range-state-background-color: #2b3035;
  --mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: #2b3035;
  --mat-datepicker-toggle-icon-color: white;
  --mat-datepicker-calendar-body-label-text-color: rgba(255, 255, 255, .7);
  --mat-datepicker-calendar-period-button-icon-color: white;
  --mat-datepicker-calendar-navigation-button-icon-color: white;
  --mat-datepicker-calendar-header-divider-color: rgba(255, 255, 255, .12);
  --mat-datepicker-calendar-header-text-color: rgba(255, 255, 255, .7);
  --mat-datepicker-calendar-date-today-outline-color: rgba(255, 255, 255, .5);
  --mat-datepicker-calendar-date-today-disabled-state-outline-color: rgba(255, 255, 255, .3);
  --mat-datepicker-calendar-date-text-color: white;
  --mat-datepicker-calendar-date-outline-color: transparent;
  --mat-datepicker-calendar-date-disabled-state-text-color: rgba(255, 255, 255, .5);
  --mat-datepicker-calendar-date-preview-state-outline-color: rgba(255, 255, 255, .24);
  --mat-datepicker-range-input-separator-color: white;
  --mat-datepicker-range-input-disabled-state-separator-color: rgba(255, 255, 255, .5);
  --mat-datepicker-range-input-disabled-state-text-color: rgba(255, 255, 255, .5);
  --mat-datepicker-calendar-container-background-color: #424242;
  --mat-datepicker-calendar-container-text-color: white;
}

/* Checkbox */
body.dark {
  /* default/accent color */
  --mdc-checkbox-disabled-selected-checkmark-color: #fff;
  --mdc-checkbox-disabled-selected-icon-color: rgba(255, 255, 255, .38);
  --mdc-checkbox-disabled-unselected-icon-color: rgba(255, 255, 255, .38);
  --mdc-checkbox-selected-checkmark-color: #d0d4d8;
  --mdc-checkbox-selected-focus-icon-color: #56585a;
  --mdc-checkbox-selected-hover-icon-color: #56585a;
  --mdc-checkbox-selected-icon-color: #56585a;
  --mdc-checkbox-selected-pressed-icon-color: #56585a;
  --mdc-checkbox-unselected-focus-icon-color: #eeeeee;
  --mdc-checkbox-unselected-hover-icon-color: #eeeeee;
  --mdc-checkbox-unselected-icon-color: rgba(255, 255, 255, .54);
  --mdc-checkbox-unselected-pressed-icon-color: rgba(255, 255, 255, .54);
  --mdc-checkbox-selected-focus-state-layer-color: #69f0ae;
  --mdc-checkbox-selected-hover-state-layer-color: #69f0ae;
  --mdc-checkbox-selected-pressed-state-layer-color: #69f0ae;
  --mdc-checkbox-unselected-focus-state-layer-color: white;
  --mdc-checkbox-unselected-hover-state-layer-color: white;
  --mdc-checkbox-unselected-pressed-state-layer-color: white;
}

/* Progress Spinner */
body.dark {
  --mdc-circular-progress-active-indicator-width: 4px;
  --mdc-circular-progress-size: 48px;
  --mdc-circular-progress-active-indicator-color: #1a1c1e;
}


/* Progress Bar */
body.dark .mat-mdc-progress-bar {
  /* default/primary color */
  --mdc-linear-progress-active-indicator-color: #1a1c1e;
  --mdc-linear-progress-track-color:  #66696b;
}

/* Radio Button */
body.dark .mat-mdc-radio-button.mat-accent {
  --mdc-radio-disabled-selected-icon-color: #fff;
  --mdc-radio-disabled-unselected-icon-color: #fff;
  --mdc-radio-unselected-hover-icon-color: #eeeeee;
  --mdc-radio-unselected-icon-color: rgba(255, 255, 255, .54);
  --mdc-radio-unselected-pressed-icon-color: rgba(255, 255, 255, .54);
  --mdc-radio-selected-focus-icon-color: #dddddd;
  --mdc-radio-selected-hover-icon-color: #808488;
  --mdc-radio-selected-icon-color: #d3d5d6;
  --mdc-radio-selected-pressed-icon-color: #808488;
  --mat-radio-ripple-color: #fff;
  --mat-radio-checked-ripple-color: #808488;
  --mat-radio-disabled-label-color: rgba(255, 255, 255, .5)
}

/* Select */
body.dark {
  /* default/primary color */
  --mat-select-panel-background-color: #424242;
  --mat-select-enabled-trigger-text-color: rgba(255, 255, 255, .87);
  --mat-select-disabled-trigger-text-color: rgba(255, 255, 255, .38);
  --mat-select-placeholder-text-color: rgba(255, 255, 255, .6);
  --mat-select-enabled-arrow-color: rgba(255, 255, 255, .54);
  --mat-select-disabled-arrow-color: rgba(255, 255, 255, .38);
  --mat-select-focused-arrow-color: rgba(206, 205, 206, 0.87);
  --mat-select-invalid-arrow-color: rgba(244, 67, 54, .87);
  --mat-minimal-pseudo-checkbox-selected-checkmark-color:#dfe7ed;
  --mat-option-selected-state-layer-color: #5a6065;
  --mdc-filled-text-field-focus-label-text-color: #898a8b;
  --mdc-filled-text-field-focus-active-indicator-color: #a8aaac;
}

/* Light Theme Option color */
body.light .mat-mdc-option .mdc-list-item__primary-text{
  color: black;
}

/* slide toggle */
body.dark .mat-mdc-slide-toggle {
  /* default/primary color */
  --mdc-switch-selected-focus-state-layer-color: #ffffff;
  --mdc-switch-selected-handle-color: #ffffff;
  --mdc-switch-selected-hover-state-layer-color: #ffffff;
  --mdc-switch-selected-pressed-state-layer-color: #ffffff;
  --mdc-switch-selected-focus-handle-color: #ffffff;
  --mdc-switch-selected-hover-handle-color: #ffffff;
  --mdc-switch-selected-pressed-handle-color: #ffffff;
  --mdc-switch-selected-focus-track-color: #757677;
  --mdc-switch-selected-hover-track-color: #757677;
  --mdc-switch-selected-pressed-track-color: #757677;
  --mdc-switch-selected-track-color: #757677;
  --mdc-switch-disabled-selected-handle-color: #000;
  --mdc-switch-disabled-selected-icon-color: #212121;
  --mdc-switch-disabled-selected-track-color: #f5f5f5;
  --mdc-switch-disabled-unselected-handle-color: #000;
  --mdc-switch-disabled-unselected-icon-color: #212121;
  --mdc-switch-disabled-unselected-track-color: #f5f5f5;
  --mdc-switch-handle-surface-color: var(--mdc-theme-surface, #fff);
  --mdc-switch-handle-elevation-shadow: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);
  --mdc-switch-handle-shadow-color: black;
  --mdc-switch-disabled-handle-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);
  --mdc-switch-selected-icon-color: #212121;
  --mdc-switch-unselected-focus-handle-color: #9e9e9e;
  --mdc-switch-unselected-focus-state-layer-color: #f5f5f5;
  --mdc-switch-unselected-focus-track-color: #616161;
  --mdc-switch-unselected-handle-color: #9e9e9e;
  --mdc-switch-unselected-hover-handle-color: #9e9e9e;
  --mdc-switch-unselected-hover-state-layer-color: #f5f5f5;
  --mdc-switch-unselected-hover-track-color: #616161;
  --mdc-switch-unselected-icon-color: #212121;
  --mdc-switch-unselected-pressed-handle-color: #fafafa;
  --mdc-switch-unselected-pressed-state-layer-color: #f5f5f5;
  --mdc-switch-unselected-pressed-track-color: #616161;
  --mdc-switch-unselected-track-color: #616161;
}


/* Slider */
body.dark {
  /* default/primary color */
  --mdc-slider-handle-color: #d4d2d4;
  --mdc-slider-focus-handle-color: #d4d2d4;
  --mdc-slider-hover-handle-color: #d4d2d4;
  --mdc-slider-active-track-color: #d4d2d4;
  --mdc-slider-inactive-track-color: #d4d2d4;
  --mdc-slider-with-tick-marks-inactive-container-color: #d4d2d4;
  --mdc-slider-with-tick-marks-active-container-color: white;
  --mdc-slider-disabled-active-track-color: #fff;
  --mdc-slider-disabled-handle-color: #fff;
  --mdc-slider-disabled-inactive-track-color: #fff;
  --mdc-slider-label-container-color: #fff;
  --mdc-slider-label-label-text-color: #000;
  --mdc-slider-with-overlap-handle-outline-color: #fff;
  --mdc-slider-with-tick-marks-disabled-container-color: #fff;
  --mdc-slider-handle-elevation: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);
  --mat-mdc-slider-ripple-color: #d4d2d4;
  --mat-mdc-slider-hover-ripple-color: rgba(156, 39, 176, .05);
  --mat-mdc-slider-focus-ripple-color: rgba(156, 39, 176, .2);
  --mat-slider-value-indicator-opacity: .9;
}


body.dark{
  /* Stepper */
  --mat-stepper-header-selected-state-icon-background-color:#f7f7f7;
  --mat-stepper-header-edit-state-icon-background-color:#f7f7f7;
  /* Tabs */
  --mdc-tab-indicator-active-indicator-color: #f7f7f7;
  --mat-tab-header-active-focus-label-text-color: #f7f7f7;
  --mat-tab-header-active-hover-label-text-color: #f7f7f7;
  --mat-tab-header-active-focus-indicator-color: #f7f7f7;
  --mat-tab-header-active-hover-indicator-color: #f7f7f7;
  /* Snack bar */
  --mat-snack-bar-button-color:#616161;
  /* Pagination */
  --mdc-outlined-text-field-focus-outline-color:#e0e0e0;
}