<div
  appHtmlClass
  [defaultColor]="theme"
  [class.example-is-mobile]="mobileQuery?.matches"
  class="main"
>
  <lib-navbar [sidebarRef]="sidebar" [theme]="theme" (clickMenuOption)="onMenuClick($event)"></lib-navbar>
  <mat-sidenav-container
    class="container"
    [style.marginTop.px]="mobileQuery?.matches ? 0 : 0"
  >
    <mat-sidenav
      opened="true"
      #sidebar
      [mode]="mobileQuery?.matches ? 'over' : 'side'"
      [fixedInViewport]="mobileQuery?.matches"
    >
      <lib-sidebar [activeUrl]="activeUrl" [menu]="menu" (clickMenuOption)="onMenuClick($event)"></lib-sidebar>
    </mat-sidenav>
    <mat-sidenav-content class="content">
      <router-outlet> </router-outlet>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
