import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { EMSIncentiveType } from '../../../../utils/meta/enum.utils';
import { IOrganization } from './organization.interface';

export interface IPeriodicDetails extends IConcept {
    aMonth: number;
    aYear: number;
    aAmount: number;
}

export interface IIncentive extends IConcept {
    tIdUser: Types.ObjectId;
    dStartDate: Date;
    dEndDate: Date;
    aTotalAmount: number;
    aPeriodAmount: number;
    tPeriodicDetails: IPeriodicDetails[];
    eType: EMSIncentiveType;
    tOrganization: string | Types.ObjectId | IOrganization;
}

export interface IIncentiveDetails extends IConcept {
    tIdUser: string | Types.ObjectId;
    eType: EMSIncentiveType;
    aTotalAmount: number;
    dStartDate: Date;
    dEndDate: string;
    dDate: Date;
    aAmount: number;
    tOrganization: string | Types.ObjectId | IOrganization;
}