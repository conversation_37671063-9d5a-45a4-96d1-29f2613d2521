<div class="overflow-x-visible">
  <div
    (scroll)="closeMenu()"
    class="flex flex-col bg-[var(--p-content-background)] overflow-y-auto items-center hide-scrollbar h-[calc(100vh-5rem)]"
  >
    <div
      *ngFor="let item of menuItems; index as currentIndex"
      #sidebarRef
      (mouseenter)="toggleMenuVisibility(currentIndex)"
      (mouseleave)="closeMenu()"
      [ngClass]="{
        'bg-[var(--active)]': currentIndex === activeMenuIndex,
        'bg-[var(--hover)]':
          currentIndex === lastMenuIndex && currentIndex !== activeMenuIndex,
        'hover:bg-[var(--hover)]': currentIndex !== activeMenuIndex,
        'mt-5': currentIndex == 0
      }"
      class="cursor-pointer w-28 py-4 rounded-md"
    >
      <div class="flex items-center justify-center flex-col">
        <span
          class="material-symbols-outlined text-[var(--p-content-color)] text-2xl"
          [ngStyle]="{
            'font-variation-settings':
              currentIndex === activeMenuIndex ? '\'FILL\' 1' : '',
            'font-size': '28px'
          }"
        >
          {{ item.icon }}
        </span>

        <span class="text-xs font-semibold"> {{ item.label }} </span>
      </div>

      <div class="absolute left-28 z-50 hidden shadow-lg" #menuRef>
        <p-menu [model]="item.children" [style]="{ padding: '0.5rem' }">
          <ng-template let-item pTemplate="item">
            <a
              (click)="navigateTo(currentIndex)"
              [routerLink]="item.link"
              [routerLinkActive]="'bg-[var(--active)]'"
              class="p-1 rounded-md flex gap-1 items-center"
            >
              <span
                class="material-symbols-outlined text-lg"
                [ngStyle]="{
                  'font-variation-settings':
                    router.url === item.link ? '\'FILL\' 1, \'wght\' 600' : ''
                }"
              >
                {{ item.icon }}
              </span>

              {{ item.label }}
            </a>
          </ng-template>
        </p-menu>
      </div>
    </div>
  </div>
</div>
