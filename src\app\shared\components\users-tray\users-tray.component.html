<div>

  <div class="flex justify-around gap-4 mt-6">
    <div *ngIf="users.length === 0" class="flex flex-col items-center mb-4">
      <img [src]="noUserImage" alt="" class="w-32 h-32 object-cover" />
      <p>{{ noUserText }}</p>
    </div>
    <ng-container *ngFor="let item of users; let index = index">
      <div
        *ngIf="index < colCount"
        class="flex flex-col items-center relative cursor-pointer outline-2 outline-offset-2 outline-orange-700"
        (click)="displayUser($event, item)"
      >
        <img
          [src]="item.image"
          alt="profile"
          class="w-12 h-12 rounded-full object-cover"
        />
        <p class="text-ellipsis w-12 whitespace-nowrap overflow-hidden">
          {{ item.name ? item.name : item.firstName }}
        </p>
        <div class="absolute right-0 -top-1">
          <app-badge [bgColor]="bgColor" [icon]="icon" />
        </div>
      </div>
    </ng-container>

    <div *ngIf="users.length > colCount" class="flex">
      <p
        (click)="showAllUsers = true"
        class="p-2 border-2 border-[var(--p-primary-400)] text-[var(--p-primary-400)] rounded-full h-12 w-12 font-bold cursor-pointer flex items-center justify-center"
      >
        <span>+</span>
        <span>{{ users.length - colCount }}</span>
      </p>
    </div>
  </div>

  <p-popover #popover (onHide)="selectedUser = undefined" styleClass="p-4">
    <div>
      <div class="flex items-center">
        <img
          [src]="selectedUser?.image"
          alt=""
          class="h-10 w-10 object-cover rounded-full"
        />
        <p class="font-semibold ml-2">{{ selectedUser?.name }}</p>
        <div class="ml-auto">
          <p-button
            icon="pi pi-external-link"
            variant="text"
            severity="secondary"
            rounded="true"
          />
          <p-button
            icon="pi pi-times"
            variant="text"
            severity="secondary"
            rounded="true"
            styleClass="ml-2"
            (onClick)="hidePopover()"
          />
        </div>
      </div>
      <p-divider class="my-4"></p-divider>
      <div class="my-4">
        <h1 class="text-2xl font-semibold">Contact Details</h1>
        <div class="grid grid-cols-2 gap-8 mt-2">
          <p class="flex items-center gap-2 text-xl">
            <span class="pi pi-envelope"></span>
            <span>{{ "<EMAIL>" }}</span>
          </p>
          <p class="flex items-center gap-2 text-xl">
            <span class="pi pi-phone"></span> <span>{{ "8336099897" }}</span>
          </p>
        </div>
      </div>
      <div class="grid grid-cols-2 mt-4">
        <div *ngFor="let item of otherInformation" class="mb-4">
          <p class="text-sm">{{ item.label | uppercase }}</p>
          <p class="text-xl">{{ item.value }}</p>
        </div>
      </div>
    </div>
  </p-popover>
</div>

<p-dialog
  [(visible)]="showAllUsers"
  [modal]="true"
  [baseZIndex]="10000"
  [style]="{ width: '40vw' }"
  [header]="heading"
>
  <div class="grid grid-cols-4 gap-x-4 gap-y-8">
    <ng-container *ngFor="let item of users">
      <div class="flex flex-col items-center cursor-pointer"  (click)="displayUser($event, item)">
        <img
          [src]="item.image"
          alt=""
          class="h-10 w-10 object-cover rounded-full"
        />
        <p class="font-semibold ml-2">{{ item.name }}</p>
      </div>
    </ng-container>
  </div>
</p-dialog>
