import { Component, inject, OnInit, Signal } from '@angular/core';
import { AvatarModule } from 'primeng/avatar';
import { OverlayBadgeModule } from 'primeng/overlaybadge';
import { MenuModule } from 'primeng/menu';
import { MenuItem, MessageService } from 'primeng/api';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { TranslatePipe } from '@ngx-translate/core';
import { marker as _ } from '@colsen1991/ngx-translate-extract-marker';
import { LanguageService } from '../../services/language/language.service';
import { ILanguage } from '../../models/Language';
import { DrawerModule } from 'primeng/drawer';
import { TicketComponent } from '../../../modules/home/<USER>/ticket/ticket.component';
import { AuthService } from '@shared/services/auth/auth.service';
import { Router } from '@angular/router';

/**
 * Predefined organization menu items.
 */
const ORGANIZATION_ITEMS: MenuItem[] = [
  {
    items: [{ label: 'Inceptial' }, { label: 'Bassetti' }],
  },
];

@Component({
  selector: 'app-header',
  imports: [
    AvatarModule,
    OverlayBadgeModule,
    MenuModule,
    DividerModule,
    ButtonModule,
    TranslatePipe,
    CommonModule,
    DrawerModule,
    TicketComponent,
  ],
  templateUrl: './header.component.html',
})
/**
 * The `HeaderComponent` manages the header section of the application, including:
 * - User profile menu with various actions
 * - Organization selection dropdown
 * - Theme toggling (dark/light mode)
 * - Language selection
 */
export class HeaderComponent implements OnInit {
  /**
   * Menu items for the user profile dropdown.
   */
  profileItems: MenuItem[] = [];

  /**
   * Menu items for selecting an organization.
   */
  organizationItems: MenuItem[] = ORGANIZATION_ITEMS;

  /**
   * Boolean indicating whether dark mode is enabled.
   */
  isDarkMode: boolean = window.matchMedia('(prefers-color-scheme: dark)')
    .matches;

  /**
   * The currently selected organization.
   * Defaults to 'Bassetti'.
   */
  organization: string = _('Bassetti');

  /**
   * Injected language service to manage translations.
   */
  languageService = inject(LanguageService);

  /**
   * The currently selected language as a reactive signal.
   */
  selectedLanguage: Signal<ILanguage> = this.languageService.selectedLanguage;

  /**
   * Menu items for selecting a language.
   */
  languageItems: MenuItem[] = this.languageService.languages.map((lang) => ({
    label: lang.name,
    command: () => this.languageService.changeLanguage(lang.code),
  }));

  isTicketDrawerVisible: boolean = false;

  private readonly _authService = inject(AuthService);

  private readonly _messageService = inject(MessageService);

  private readonly _router = inject(Router);

  /**
   * Initializes the component by applying the user's theme preference.
   */
  ngOnInit() {
    this.applyTheme();
    this.intializeProfileMenuItems();
  }

  intializeProfileMenuItems() {
    this.profileItems = [
      { label: 'My Profile', icon: 'pi pi-user', routerLink: '' },
      { label: 'Calendar', icon: 'pi pi-calendar', routerLink: '' },
      { label: 'Organization Setting', icon: 'pi pi-cog', routerLink: '' },
      {
        label: 'Help & IT Support',
        icon: 'pi pi-question-circle',
        routerLink: '',
      },
      {
        label: 'Logout',
        icon: 'pi pi-sign-out',
        routerLink: '',
        command: () => this.logout(),
      },
    ];
  }

  /**
   * Applies the theme based on the user's saved preference.
   * It reads from `localStorage` and updates the `isDarkMode` property accordingly.
   */
  applyTheme() {
    const theme = localStorage.getItem('theme');
    this.isDarkMode = theme ? theme === 'dark' : this.isDarkMode;
    document.documentElement.classList.toggle('my-app-dark', this.isDarkMode);
    document.documentElement.classList.toggle('my-app-light', !this.isDarkMode);
  }

  /**
   * Toggles between dark mode and light mode.
   * Saves the preference to `localStorage` and updates the class on the root element.
   */
  toggleDarkMode() {
    this.isDarkMode = !this.isDarkMode;
    localStorage.setItem('theme', this.isDarkMode ? 'dark' : 'light');
    document.documentElement.classList.toggle('my-app-dark');
    document.documentElement.classList.toggle('my-app-light');
  }

  /**
   * Updates the currently selected organization.
   *
   * @param organization The name of the organization to select.
   */
  selectOrganization(organization: string) {
    this.organization = organization;
  }

  getFlag(code: string) {
    return `assets/images/${code}.webp`;
  }

  logout() {
    this._authService.logout();
    this._messageService.add({
      severity: 'success',
      summary: 'Logout',
      detail: 'You have been logged out successfully.',
    });
    this._router.navigate(['/login']);
  }
}
