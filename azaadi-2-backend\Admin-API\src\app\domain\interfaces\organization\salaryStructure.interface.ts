import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';

export interface ISalaryStructure extends IConcept {
    tIdUser: Types.ObjectId;
    tSalaryStructure: any;
    eType: string;
    bHavingAdvancePayment: boolean;
}

export interface IUpdatedSalaryStructure extends IConcept {
    tIdUser: Types.ObjectId;
    tSalaryStructure: any;
    eType: string;
    bHavingAdvancePayment: boolean;
    tOrganization: Types.ObjectId;
    isActivePF: boolean;
    isActiveESI: boolean;
}

export interface ISalaryStructureTemplate {
    tSalaryStructure?: Record<string, any>; // Using any for mongoose.Schema.Types.Mixed
    eType?: string; // Note: Should be restricted to emsSalaryStructureCategory enum when available
}