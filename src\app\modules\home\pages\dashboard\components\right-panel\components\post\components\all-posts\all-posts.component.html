<div class="max-h-96 overflow-y-auto p-4">
    <div *ngFor="let post of posts(); let i = index" [ngClass]="{ 'mt-4': i > 0 }">
        <div class="gap-2 mb-2 border-[var(--border-color)] border-[2px] p-4 rounded-xl">
            <div class="flex gap-2 items-center">
                <img [src]="post.user.image" alt="" class="h-10 w-10 object-cover rounded-full" />
                <div>
                    <p class="font-semibold">{{ post.user.name }}</p>
                    <p>{{ post.date | date : "HH:mm a" }}</p>
                </div>
            </div>
            <p class="my-4">{{ post.body }}</p>
            <div class="flex gap-8">
                <p class="flex items-center gap-2 cursor-pointer">
                    <span class="pi pi-thumbs-up"></span>
                    <span>Like</span>
                </p>
                <p class="flex items-center gap-2 cursor-pointer">
                    <span class="pi pi-comment"></span>
                    <span>Comment</span>
                </p>
            </div>
        </div>
    </div>
</div>