<div
  class="flex items-center justify-center gap-12 border-[1px] p-4 rounded-md border-[var(--border-color)]"
>
  <div class="flex-auto">
    <label for="buttondisplay" class="font-bold block mb-2"> From </label>
    <p-datepicker
      [(ngModel)]="startDate"
      [showIcon]="true"
      inputId="buttondisplay"
      [showOnFocus]="false"
    />
  </div>

  <div
    class="border-[1px] h-12 w-16 flex border-[var(--border-color)] items-center justify-center text-[var(--p-text-color)] rounded-md"
  >
    {{ calculateTotalDays() }}
  </div>

  <div class="flex-auto">
    <label for="buttondisplay" class="font-bold block mb-2 text-right">
      To
    </label>
    <p-datepicker
      [(ngModel)]="endDate"
      [showIcon]="true"
      inputId="buttondisplay"
      [showOnFocus]="false"
    />
  </div>
</div>
<div class="mt-4">
  <label for="reason">Reason</label>
  <textarea
    [(ngModel)]="reason"
    id="reason"
    rows="5"
    pTextarea
    class="w-full"
    placeholder="Type here"
    >{{ reason }}</textarea
  >
</div>

<div class="mt-4">
  <p>Notify</p>
  <input
    pInputText
    [(ngModel)]="searchedEmployee"
    type="text"
    pSize="large"
    placeholder="Search Employee"
    class="w-full"
  />
</div>

<div class="flex justify-end gap-4 my-2 absolute bottom-0 right-2">
  <p-button outlined="true" (onClick)="closeWorkFromHomeDrawer()"
    >Cancel</p-button
  >
  <p-button>Submit</p-button>
</div>
