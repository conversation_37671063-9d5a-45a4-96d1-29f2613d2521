import { model, Model, models, Schema, Types } from "mongoose";
import { ISalary, ISalaryHistory } from "../../interfaces/organization/salaryReport.interface";
/**
 * SalaryHistory model class that defines the schema and provides access to the SalaryHistory collection.
 * 
 * @class SalaryHistory
 * @description Handles the schema definition and model creation for Salary History
 */
export default class SalaryHistory {
    /**
     * Mongoose schema definition for Salary
     * @private
     */
    private static _salarySchema:Schema<ISalary> = new Schema<ISalary>(
        {
            tSalary: {
                type: Schema.Types.Mixed
            },
            dStartDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            dEndDate: {
                type: Date,
                index: true // Indexed for date-based queries
            }
        }, 
        { 
            timestamps: true,
            _id: true // Maintain separate IDs for each salary record
        }
    );

    /**
     * Mongoose schema definition for SalaryHistory
     * @private
     */
    private static _schema:Schema<ISalaryHistory> = new Schema<ISalaryHistory>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                ref: "login",
                required: [true, "User ID is required"],
                trim: true,
                index: true // Indexed for faster lookups by user
            },
            tSalaries: {
                type: [SalaryHistory._salarySchema],
                default: []
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                required: [true, "Organization is required"],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for SalaryHistory
     * @private
     */
    private static _model: Model<ISalaryHistory> = models?.salaryhistory || model<ISalaryHistory>("salaryhistory", SalaryHistory._schema);

    /**
     * Get the Mongoose model for SalaryHistory
     * @returns {Model<ISalaryHistory>} The SalaryHistory model
     */
    public static get model(): Model<ISalaryHistory> {
        return SalaryHistory._model;
    }
}