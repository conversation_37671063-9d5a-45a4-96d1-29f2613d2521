<div class="flex gap-4" *ngIf="type === 'text'">
  <p-select
    [options]="filters"
    [formControl]="selectedFilter"
    optionLabel="header"
    optionValue="field"
    placeholder="Select a filter"
    class="w-full md:w-56"
    variant="filled"
  ></p-select>

  <input
    pInputText
    id="on_label"
    [formControl]="searchedValue"
    autocomplete="off"
    class="flex-1"
    variant="filled"
    placeholder="Search..."
  />
</div>

<div *ngIf="type === 'date'">
  <p-floatlabel variant="on">
    <p-datepicker
      [(ngModel)]="selectedDate"
      [showIcon]="true"
      inputId="buttondisplay"
      [showOnFocus]="false"
      (ngModelChange)="onDateSelected($event)"
    />
    <label for="over_label">{{ dateLabel }}</label>
  </p-floatlabel>
</div>
