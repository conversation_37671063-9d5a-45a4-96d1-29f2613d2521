import { IUserRequest } from "../../interfaces/user/request.interface";
import { UserRequest } from "../../models";
import Repository from "../repository";
import IUserRequestRepository from "./abstract/userRequestRepository.abstract";

export default class UserRequestRepository extends Repository<IUserRequest> implements IUserRequestRepository {
    constructor() {
        super(UserRequest.model);
    }
}