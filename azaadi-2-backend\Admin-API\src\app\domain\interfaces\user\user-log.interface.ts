import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { IUser } from './user.interface';
import { IOrganization } from '../organization/organization.interface';
import { EmsLogOperationType } from '../../../../utils/meta/enum.utils';

export interface ILogData{
    sMetaKey: string;
    sMetaValue: string|number|boolean|object;
}

export interface IUserLog extends IConcept {
    tIdUser: string | Types.ObjectId | IUser;
    tOrganization?: string | Types.ObjectId | IOrganization;
    tPreviousData?: ILogData[];
    tCurrentData?: ILogData[];
    sOperationType: EmsLogOperationType;
    dOperationDate: Date;
}