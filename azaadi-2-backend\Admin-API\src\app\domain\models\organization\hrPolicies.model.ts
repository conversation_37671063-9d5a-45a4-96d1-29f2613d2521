import { Schema, model, Model, models, Types } from 'mongoose';
import { IHRPolicy } from '../../interfaces/organization/policies.interface';

/**
 * HRPolicies model class that defines the schema and provides access to the HRPolicies collection.
 * 
 * @class HRPolicies
 * @description Handles the schema definition and model creation for HR Policies
 */
export default class HRPolicies {
    /**
     * Mongoose schema definition for HRPolicies
     * @private
     */
    private static _schema: Schema<IHRPolicy> = new Schema<IHRPolicy>(
        {
            sName: {
                type: String,
                required: [true, 'Policy name is required'],
                trim: true,
                index: true // Indexed for faster search and filtering by policy name
            },
            sContent: {
                type: String,
                required: [true, 'Policy content is required'],
                trim: true
            },
            tRole: {
                type: Schema.Types.ObjectId,
                ref: 'role',
                required: [true, 'Role is required'],
                trim: true,
                index: true // Indexed for faster lookups and joins by role
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                required: [true, 'Organization is required'],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for HRPolicies
     * @private
     */
    private static _model: Model<IHRPolicy> = models?.HRPolicy || model<IHRPolicy>('hrpolicy', HRPolicies._schema);

    /**
     * Get the Mongoose model for HRPolicies
     * @returns {Model<IHRPolicy>} The HRPolicies model
     */
    public static get model(): Model<IHRPolicy> {
        return HRPolicies._model;
    }
}