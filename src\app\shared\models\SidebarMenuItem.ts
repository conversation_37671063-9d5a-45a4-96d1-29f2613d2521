/**
 * Represents a menu item in the sidebar navigation.
 * Each menu item can have a label, an icon, an optional link, and optional child menu items.
 */
class SidebarMenuItem {
  /**
   * Creates an instance of `SidebarMenuItem`.
   *
   * @param _label - The label text for the menu item.
   * @param _icon - The icon associated with the menu item.
   * @param _link - (Optional) The URL link for navigation.
   * @param _children - (Optional) An array of child `SidebarMenuItem` objects representing a nested menu structure.
   */
  constructor(
    private _label: string,
    private _icon: string,
    private _link?: string,
    private _children?: SidebarMenuItem[]
  ) {}

  /**
   * Gets the label of the menu item.
   */
  get label(): string {
    return this._label;
  }

  /**
   * Gets the icon of the menu item.
   */
  get icon(): string {
    return this._icon;
  }

  /**
   * Gets the navigation link for the menu item, if available.
   */
  get link(): string | undefined {
    return this._link;
  }

  /**
   * Gets the child menu items, if any.
   */
  get children(): SidebarMenuItem[] | undefined {
    return this._children;
  }

  /**
   * Sets the label of the menu item.
   */
  set label(value: string) {
    this._label = value;
  }

  /**
   * Sets the icon of the menu item.
   */
  set icon(value: string) {
    this._icon = value;
  }

  /**
   * Sets the navigation link for the menu item.
   */
  set link(value: string | undefined) {
    this._link = value;
  }

  /**
   * Sets the child menu items.
   */
  set children(value: SidebarMenuItem[] | undefined) {
    this._children = value;
  }

  /**
   * Converts a plain object to a `SidebarMenuItem` instance.
   *
   * @param json - The plain object containing menu item properties.
   * @returns A new `SidebarMenuItem` instance.
   */
  static fromJSON(json: any): SidebarMenuItem {
    return new SidebarMenuItem(
      json.label,
      json.icon,
      json.link,
      json.children ? json.children.map(SidebarMenuItem.fromJSON) : undefined
    );
  }
}

export default SidebarMenuItem;
