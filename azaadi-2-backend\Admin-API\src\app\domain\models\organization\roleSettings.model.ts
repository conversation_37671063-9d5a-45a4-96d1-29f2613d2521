import { model, Model, models, Schema } from "mongoose";
import { IRoleSettings } from "../../interfaces/organization/role.interface";
import { EMSRoleSettingLabel } from "../../../../utils/meta/enum.utils";

/**
 * RoleSettings model class that defines the schema and provides access to the RoleSettings collection.
 * 
 * @class RoleSettings
 * @description Handles the schema definition and model creation for Role Settings
 */
export default class RoleSettings {
    /**
     * Mongoose schema definition for RoleSettings
     * @private
     */
    private static _schema = new Schema<IRoleSettings>(
        {
            tRole: {
                type: Schema.Types.ObjectId,
                required: [true, "Please give tRole"],
                trim: true,
                ref: "role",
                index: true // Indexed for faster lookups and joins by role
            },
            sTitle: {
                type: String,
                trim: true,
                required: [true, "Role Settings title is missing"],
                enum: EMSRoleSettingLabel,
                index: true // Indexed for quick lookups by setting title
            },
            sType: {
                type: String,
                trim: true,
                index: true // Indexed for filtering by setting type
            },
            sValue: {
                type: Schema.Types.Mixed,
                trim: true
            },
            tManagedBy: {
                type: Schema.Types.ObjectId,
                ref: "role",
                trim: true,
                index: true // Indexed for hierarchy-based queries
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for RoleSettings
     * @private
     */
    private static _model: Model<IRoleSettings> = models?.RoleSettings || model<IRoleSettings>("rolesettings", RoleSettings._schema);

    /**
     * Get the Mongoose model for RoleSettings
     * @returns {Model<IRoleSettings>} The RoleSettings model
     */
    public static get model(): Model<IRoleSettings> {
        return RoleSettings._model;
    }
}