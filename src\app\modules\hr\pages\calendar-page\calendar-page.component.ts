import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { CalendarViewComponent } from './components/calendar-view/calendar-view.component';
import { DividerModule } from 'primeng/divider';
import { CardModule } from 'primeng/card';
import { LeaveBalanceCardComponent } from './components/leave-balance-card/leave-balance-card.component';
import { LeaveBalanceCardInput } from './models/leave_balance_card_input';
import { TableComponent } from './components/table/table.component';
import { SelectButton } from 'primeng/selectbutton';
import { FormsModule } from '@angular/forms';
import { ToggleSwitch } from 'primeng/toggleswitch';

@Component({
  selector: 'app-calendar-page',
  imports: [
    CalendarViewComponent,
    CommonModule,
    DividerModule,
    CardModule,
    LeaveBalanceCardComponent,
    TableComponent,
    SelectButton,
    FormsModule,
    ToggleSwitch,
  ],
  templateUrl: './calendar-page.component.html',
})
export class CalendarPageComponent {
  leavebalance_data: LeaveBalanceCardInput[] = [
    {
      label: 'Casual Leave',
      totalDays: 12,
      daysAvailable: 8,
      color: 'text-[#F472B6]',
    },
    {
      label: 'Sick Leave',
      totalDays: 8,
      daysAvailable: 8,
      color: 'text-[#95D16A]',
    },
    { label: 'WFH', totalDays: 24, daysAvailable: 12, color: 'text-[#FBBF24]' },
  ];
  stateOptions: any[] = [
    { label: 'ATTENDANCE', value: 'ATTENDANCE' },
    { label: 'REQUESTS', value: 'REQUESTS' },
  ];
  value: string = 'off';
  checked: boolean = false;
}
