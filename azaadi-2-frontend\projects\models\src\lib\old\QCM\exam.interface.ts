import { EmsUser } from '../user.interface';
import { EmsTest } from './test.interface';
import { EmsContent } from '../content.interface';
export interface EmsExam {
    _id?: string;
    sName?: string;
    tTest?: string | EmsTest;
    sUrl?: string;
    tUser?: string | EmsUser;
    tExaminees?: string[] | EmsUser[];
    dStartDateTime?: Date;
    dEndDateTime?: Date;
    aTime?: number;
    bIsAllowTime?: boolean;
    bIsController?: boolean;
    bAllowWebcam?: boolean;
    bAllowGuest?: boolean;
    aUsersCount?: number;
    createdAt?: Date;
    updatedAt?: Date;
    tLoginContent?: EmsContent | string;
    tInstructionContent?: EmsContent | string;
    tSubmissionContent?: EmsContent | string;
    bIsPublised?: boolean;
    bWithQna?: boolean;
    bIsSecured?: boolean;
}
