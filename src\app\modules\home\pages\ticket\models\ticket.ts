import { IUser } from '@shared/models';

export interface ITicket {
  id: number;
  title: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  createdAt: Date;
  department: TicketDepartment;
  assignedTo: IUser;
  raisedBy: IUser;
  comments: ITicketComment[];
  attachments: string[];
}

export enum TicketStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
}

export enum TicketPriority {
  LOW_PRIORITY = 'LOW PRIORITY',
  MEDIUM_PRIORITY = 'MEDIUM PRIORITY',
  HIGH_PRIORITY = 'HIGH PRIORITY',
}

export enum TicketDepartment {
  IT_DEPARTMENT = 'IT DEPARTMENT',
  HR_DEPARTMENT = 'HR DEPARTMENT',
  FINANCE_DEPARTMENT = 'FINANCE DEPARTMENT',
  DEV_DEPARTMENT = 'DEV DEPARTMENT',
}
export interface ITicketComment {
  id: number;
  comment: string;
  createdAt: Date;
  createdByUserId: number;
  ticket: ITicket;
}
