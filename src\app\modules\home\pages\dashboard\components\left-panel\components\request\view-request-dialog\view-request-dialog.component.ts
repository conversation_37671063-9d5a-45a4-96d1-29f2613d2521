import { Component, inject, OnInit } from '@angular/core';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import {
  IRequest,
  RequestStatus,
  RequestType,
} from '../../../../../models/request';
import { CommonModule } from '@angular/common';
import { DividerModule } from 'primeng/divider';
import { TagModule } from 'primeng/tag';

@Component({
  selector: 'app-view-request-dialog',
  imports: [CommonModule, DividerModule, TagModule],
  templateUrl: './view-request-dialog.component.html',
  styleUrl: './view-request-dialog.component.scss',
})
export class ViewRequestDialogComponent {
  config: DynamicDialogConfig = inject(DynamicDialogConfig);
  request: IRequest = this.config.data;

  getRequestTypeLabel(): string {
    switch (this.request.eType) {
      case RequestType.SICK_LEAVE:
        return 'Sick Leave';
      case RequestType.CASUAL_LEAVE:
        return 'Casual Leave';
      case RequestType.WORK_FROM_HOME:
        return 'Work From Home';
      default:
        return '';
    }
  }

  getRequestStatusLabel(): string {
    switch (this.request.eStatus) {
      case RequestStatus.PENDING:
        return 'Pending';
      case RequestStatus.APPROVED:
        return 'Approved';
      case RequestStatus.REJECTED:
        return 'Rejected';
      default:
        return '';
    }
  }

  getStatusSeverity(): any {
    switch (this.request.eStatus) {
      case RequestStatus.PENDING:
        return 'warn';
      case RequestStatus.APPROVED:
        return 'success';
      case RequestStatus.REJECTED:
        return 'danger';
      default:
        return undefined;
    }
  }
}
