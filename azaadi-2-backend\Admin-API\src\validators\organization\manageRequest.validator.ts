import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain, validationResult } from "express-validator";
import { EMSAdvanceStatus, EMSDeductType } from "../../utils/meta/enum.utils";

/**
 * Class to handle request management-related request validations
 */
export class ManageRequestValidator {
    /**
     * Get validation rules for role request management
     */
    public static getManageRoleRequestValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Role validation
            body("tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // Count validation
            body("aCount")
                .notEmpty()
                .withMessage("Count is required")
                .isInt({ min: 0 })
                .withMessage("Count must be a non-negative integer"),

            // Type validation (optional)
            body("sType")
                .optional()
                .isString()
                .withMessage("Type must be a string")
                .trim(),

            // Approved by validation (optional)
            body("tApprovedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid approver role ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for user request management
     */
    public static getManageUserRequestValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // User ID validation
            body("tIdUser")
                .notEmpty()
                .withMessage("User ID is required")
                .isMongoId()
                .withMessage("Invalid user ID format"),

            // Request type validation
            body("tType")
                .notEmpty()
                .withMessage("Request type is required")
                .isMongoId()
                .withMessage("Invalid request type ID format"),

            // Count validation
            body("aCount")
                .notEmpty()
                .withMessage("Count is required")
                .isInt({ min: 0 })
                .withMessage("Count must be a non-negative integer"),

            // Description validation (optional)
            body("sDescription")
                .optional()
                .isString()
                .withMessage("Description must be a string")
                .trim(),

            // Role validation (optional)
            body("tRole")
                .optional()
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for advance monthly detail
     */
    private static getAdvanceMonthlyDetailValidators(): ValidationChain[] {
        return [
            body("monthlyDetails.*.aMonth")
                .isInt({ min: 1, max: 12 })
                .withMessage("Month must be between 1 and 12"),

            body("monthlyDetails.*.aYear")
                .isInt({ min: 2000 })
                .withMessage("Year must be a valid year after 2000"),

            body("monthlyDetails.*.aAmount")
                .isFloat({ min: 0 })
                .withMessage("Amount must be a positive number"),

            body("monthlyDetails.*.bIsDeducted")
                .optional()
                .isBoolean()
                .withMessage("IsDeducted must be a boolean")
        ];
    }

    /**
     * Get validation rules for attachments
     */
    private static getAttachmentValidators(): ValidationChain[] {
        return [
            body("tAttachments.*.sFilename")
                .notEmpty()
                .withMessage("Filename is required")
                .isString()
                .withMessage("Filename must be a string")
                .trim(),

            body("tAttachments.*.sPath")
                .notEmpty()
                .withMessage("File path is required")
                .isString()
                .withMessage("File path must be a string")
                .trim()
        ];
    }

    /**
     * Get validation rules for advance request management
     */
    public static getAdvanceRequestValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // User ID validation
            body("tIdUser")
                .notEmpty()
                .withMessage("User ID is required")
                .isMongoId()
                .withMessage("Invalid user ID format"),

            // Total amount validation
            body("aTotalAmount")
                .optional()
                .isFloat({ min: 0 })
                .withMessage("Total amount must be a positive number"),

            // Status validation
            body("eStatus")
                .optional()
                .isIn(Object.values(EMSAdvanceStatus))
                .withMessage("Invalid status. Must be one of: Request, Approved, Pending, HR_Approved, Rejected, Canceled, or Completed"),

            // Deduct type validation
            body("eDeductType")
                .optional()
                .isIn(Object.values(EMSDeductType))
                .withMessage("Invalid deduct type. Must be one of: Monthly, Quarterly, Half Yearly, or Yearly"),

            // Start date validation
            body("dStartDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid start date format")
                .toDate(),

            // End date validation
            body("dEndDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid end date format")
                .toDate()
                .custom((value, { req }) => {
                    if (value && req.body.dStartDate && new Date(value) <= new Date(req.body.dStartDate)) {
                        throw new Error('End date must be after start date');
                    }
                    return true;
                }),

            // Monthly details validation
            body("monthlyDetails")
                .optional()
                .isArray()
                .withMessage("Monthly details must be an array"),

            // HR approver validation
            body("tHrApprovedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid HR approver ID format"),

            // Accounts approver validation
            body("tAccApprovedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid accounts approver ID format"),

            // Disbursement date validation
            body("dDisbursementDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid disbursement date format")
                .toDate(),

            // Approve date validation
            body("dApproveDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid approve date format")
                .toDate(),

            // Reason validation
            body("sReason")
                .optional()
                .isString()
                .withMessage("Reason must be a string")
                .trim(),

            // Reply validation
            body("sReply")
                .optional()
                .isString()
                .withMessage("Reply must be a string")
                .trim(),

            // Attachments validation
            body("tAttachments")
                .optional()
                .isArray()
                .withMessage("Attachments must be an array"),

            // Organization validation
            body("tOrganization")
                .optional()
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Disbursement month validation
            body("aDisbursementMonth")
                .optional()
                .isInt({ min: 1, max: 12 })
                .withMessage("Disbursement month must be between 1 and 12"),

            // Disbursement year validation
            body("aDisbursementYear")
                .optional()
                .isInt({ min: 2000 })
                .withMessage("Disbursement year must be a valid year after 2000"),

            // Add nested validators
            ...this.getAdvanceMonthlyDetailValidators(),
            ...this.getAttachmentValidators(),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
