<div class="mb-10" *ngFor="let item of data()">
  <div class="flex items-center gap-2">
    <span
      class="inline-flex items-center text-[13px] font-semibold text-gray-500"
      ><img
        [src]="item?.icon"
        alt="img"
        class="w-[16px] h-[16px] mr-1"
      />{{ item?.title }}</span
    >
    <p-tag severity="success" value="Verified" [rounded]="true" />
  </div>
  <div class="mb-2 h-1 bg-[#239595] w-20 rounded-full"></div>
  <div class="grid grid-cols-2 text-[14px] gap-2 space-y-3 mb-5">
    <div class="hidden"></div>
    <div *ngFor="let value of item?.values" class="scale-90">
        <span class="font-medium text-[#799dad]">{{ value?.label }}</span
        ><br />
        <span class="font-semibold text-[{{ value?.valueHexColor }}]">{{
          value?.value
        }}</span>
      </div>
  </div>
</div>
