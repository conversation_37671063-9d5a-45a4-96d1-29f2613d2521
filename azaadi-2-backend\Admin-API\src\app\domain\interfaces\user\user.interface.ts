import { Types } from "mongoose";
import { IConcept, IConceptBase, INameConcept } from "../concept.interface";
import { IBranch, IOrganization } from "../organization/organization.interface";
import { IPartialRole, IRole } from "../organization/role.interface";
import { IShift } from "../organization/shift.interface";
import { IEmployeeCode, IPartialEmployeeCode } from "./employee-code.interface";
import { IDepartment, IDesignation } from "../organization/department.interface";
import { ISalaryStructure, IUpdatedSalaryStructure } from "../organization/salaryStructure.interface";

export interface IEducationInfo extends IConcept, INameConcept {
    sCourse: string;
    dStartDate: Date;
    dEndDate: Date;
    bPursuing: boolean;
    aTotalMarks: number;
    aObtainMarks: number;
}

export interface IExperienceInfo extends IConcept, INameConcept {
    sDepartment: string;
    sDesignation: string;
    sAddress: string;
    dStartDate: Date;
    dEndDate: Date;
    bCurrent: boolean;
}

export interface IUserDetails extends IConcept, INameConcept {
    aAltPhoneNo?: number;
    sPersonalEmail?: string;
    sGender?: string;
    dDob?: Date;
    dJoinDate?: Date;
    dLeaveDate?: Date;
    sPresentAddress?: string;
    sPermanentAddress?: string;
    sFatherName?: string;
    aFatherMobileNo?: number;
    aAadharNo?: number;
    sPanNo?: string;
    sPassportNo?: string;
    sPassportExpDate?: Date;
    sNationality?: string;
    sReligion?: string;
    sMaritalStatus?: string;
    sEmploymentSpouse?: string;
    aNoChildren?: number;
    bIsActive: boolean;
    sUserApplyType?: string;
    tEducationInfo?: IEducationInfo[];
    tExperienceInfo?: IExperienceInfo[];
    metaData?: string[];
    tSalaryStructure?: string | Types.ObjectId | ISalaryStructure;
    tUpdatedSalaryStructure?: string | Types.ObjectId | IUpdatedSalaryStructure;
    sPfNo?: string;
    sEsiNo?: string;
    sMotherName?: string;
    aEmergencyContactNo?: number;
    sEmergencyContactName?: string;
}

export interface IUser extends IConcept {
    tIdEmployee: string | Types.ObjectId | IEmployeeCode;
    sPassword: string;
    sEmail: string;
    aPhoneNumber: number;
    sWorkingType?: string;
    sProfileUrl?: string;
    tBranches?: string[] | Types.ObjectId[] | IBranch[];
    tOrganizations?: string[] | Types.ObjectId[] | IOrganization[];
    tRole?: string | Types.ObjectId | IRole;
    tDepartment?: string | Types.ObjectId | IDepartment;
    tDesignation?: string | Types.ObjectId | IDesignation;
    tUserDetails?: string | Types.ObjectId | IUserDetails;
    bIsActive: boolean;
    bCanLogin: boolean;
    bOnlyOfficePunch: boolean;
    tShift?: string[] | Types.ObjectId[] | IShift[];
    bIsApplicableAdvance: boolean;
    dInactiveDate?: Date;
    bIsResigned: boolean;
    bIsCreatedBySuperAdmin: boolean;
    bIsPermanentWFH: boolean;
}

export interface IPartialUser extends INameConcept {
    tIdUser?: string;
    sEmail?: string;
    aPhoneNumber?: string;
    sProfileUrl?: string;
    dInactiveDate?: Date;
    dJoinDate?: Date;
    aAadharNo?: string;
    tDepartment?: IConceptBase;
    tDesignation?: IConceptBase;
    tIdEmployee?: IPartialEmployeeCode;
    tRole?: IPartialRole;
}

export interface ISuperAdmin extends IConcept {
    sName?: string;
    sEmail: string;
    sPassword: string;
    aPhoneNumber: number;
    sProfileUrl?: string;
    bIsActive: boolean;
    bCanLogin: boolean;
    bIsDeveloper: boolean;
}

