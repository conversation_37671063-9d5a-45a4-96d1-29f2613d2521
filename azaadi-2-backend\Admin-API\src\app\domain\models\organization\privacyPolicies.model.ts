import { Schema, model, Model, models } from 'mongoose';
import { IPrivacyPolicy } from '../../interfaces/organization/policies.interface';

/**
 * PrivacyPolicies model class that defines the schema and provides access to the PrivacyPolicies collection.
 * 
 * @class PrivacyPolicies
 * @description Handles the schema definition and model creation for Privacy Policies
 */
export default class PrivacyPolicies {
    /**
     * Mongoose schema definition for PrivacyPolicies
     * @private
     */
    private static _schema = new Schema<IPrivacyPolicy>(
        {
            sName: {
                type: String,
                required: [true, 'Policy name is required'],
                trim: true,
                index: true // Indexed for faster search and filtering by policy name
            },
            sContent: {
                type: String,
                required: [true, 'Policy content is required'],
                trim: true
            },
            tRole: {
                type: Schema.Types.ObjectId,
                ref: 'role',
                required: [true, 'Role is required'],
                trim: true,
                index: true // Indexed for faster lookups and joins by role
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                required: [true, 'Organization is required'],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for PrivacyPolicies
     * @private
     */
    private static _model: Model<IPrivacyPolicy> = models?.PrivacyPolicy || model<IPrivacyPolicy>('privacypolicy', PrivacyPolicies._schema);

    /**
     * Get the Mongoose model for PrivacyPolicies
     * @returns {Model<IPrivacyPolicy>} The PrivacyPolicies model
     */
    public static get model(): Model<IPrivacyPolicy> {
        return PrivacyPolicies._model;
    }
}