import { CommonModule } from '@angular/common';
import { Component, effect, inject, Input } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { LanguageService } from '@shared/services';
import { MenuItem } from 'primeng/api';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { CardModule } from 'primeng/card';

@Component({
  selector: 'app-breadcrumb',
  imports: [CardModule, BreadcrumbModule, RouterModule, CommonModule],
  templateUrl: './breadcrumb.component.html',
})
/**
 * The `BreadcrumbComponent` displays a breadcrumb navigation trail with a required heading.
 * It helps users understand their current location within the application.
 */
export class BreadcrumbComponent {
  /**
   * The breadcrumb items to display.
   * Each item should follow the `MenuItem` structure from PrimeNG.
   *
   * @required This input must be provided.
   */
  @Input({ required: true }) items: MenuItem[] = [];

  /**
   * The heading text displayed above the breadcrumb navigation.
   *
   * @required This input must be provided.
   */
  @Input({ required: true }) heading: string = '';

  translatedHeading: string = '';

  translatedItems: MenuItem[] = [];

  private readonly _translateService: any = inject(TranslateService);

  private readonly _languageService = inject(LanguageService);

  constructor() {
    effect(() => this._translate());
  }

  ngOnInit() {
    this._translate();
  }

  /**
   * @param label The key of the translation to look up.
   * @returns The translated value of the given label.
   */

  private _translate() {
    this.translatedItems = this.items.map((item) => ({ ...item }));
    const lang = this._languageService.selectedLanguage().code;
    this._translateService.use(lang);

    // Translate the heading
    this._translateService
      .get(this.heading)
      .subscribe((res: string) => (this.translatedHeading = res));

    // Translate breadcrumb items
    this.translatedItems.forEach((item: MenuItem) => {
      if (item.label && item.label.trim()) {
        this._translateService.get(item.label).subscribe((res: string) => {
          item.label = res;
        });
      }
    });
  }
}
