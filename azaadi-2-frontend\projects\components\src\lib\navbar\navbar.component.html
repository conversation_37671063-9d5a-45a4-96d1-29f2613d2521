<mat-toolbar class="header">
    <button mat-icon-button aria-label (click)="sidebarRef?.toggle()">
      <mat-icon>menu</mat-icon>
    </button>
    <h1 >Responsive App</h1>
    <span class="example-spacer"></span>
    <div [matMenuTriggerFor]="sMenu!.menu">
      <img class="header-image" src="https://material.angular.io/assets/img/examples/shiba1.jpg" alt="" srcset="">
    </div>
    <lib-profile-menu [theme]="theme" (clickMenuOption)="onMenuOptionClick($event)"></lib-profile-menu>
</mat-toolbar>