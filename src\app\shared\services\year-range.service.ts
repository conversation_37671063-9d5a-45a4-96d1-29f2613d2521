import { Injectable } from '@angular/core';

@Injectable()
export class YearRangeService {
  private centerYear: number;

  constructor() {
    this.centerYear = new Date().getFullYear();
  }

  // Get 20 years range (10 before and 10 after center)
  getYears(): number[] {
    const start = this.centerYear - 7;
    return Array.from({ length: 12 }, (_, i) => start + i);
  }

  // Move 20 years forward
  next(): number[] {
    this.centerYear += 12;
    return this.getYears();
  }

  // Move 20 years backward
  previous(): number[] {
    this.centerYear -= 12;
    return this.getYears();
  }

  // Optionally reset to current year
  reset(): number[] {
    this.centerYear = new Date().getFullYear();
    return this.getYears();
  }

  // Optionally get center year
  getCenterYear(): number {
    return this.centerYear;
  }
}
