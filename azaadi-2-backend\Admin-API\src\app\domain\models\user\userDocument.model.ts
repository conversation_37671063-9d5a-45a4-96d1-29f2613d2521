import { Schema, model, Model, models } from 'mongoose';
import { EMSFileType } from '../../../../utils/meta/enum.utils';
import { IDocumentDetails, IUserDocuments } from '../../interfaces/user/user-document.interface';

/**
 * UserDocument model class that defines the schema and provides access to the UserDocument collection.
 * 
 * @class UserDocument
 * @description Handles the schema definition and model creation for User Documents
  */
export default class UserDocument {
    /**
     * Mongoose schema definition for document details
     * Each document can have multiple versions or related files stored in tDocumentDetails
     * 
     * @private
          */
    private static readonly _documentDetailsSchema:Schema<IDocumentDetails> = new Schema<IDocumentDetails>(
        {
            sName: {
                type: String,
                trim: true,
                required: [true, "Document name is required"],
                maxlength: [255, "Document name cannot exceed 255 characters"] // Prevents extremely long filenames
            },
            sSize: {
                type: String,
                trim: true
            },
            sUrl: {
                type: String,
                trim: true
            },
            eType: {
                type: String,
                enum: EMSFileType,
            },
            bStatus: {
                type: Boolean,
                default: true
            }
        }, 
        { 
            timestamps: true,
            _id: true // Maintain separate IDs for document details
        }
    );

    /**
     * Mongoose schema definition for user documents
     * Main schema that stores document metadata and references
     * 
     * @private
          */
    private static readonly _schema:Schema<IUserDocuments> = new Schema<IUserDocuments>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                ref: 'login',
                required: [true, "User ID is required"],
                trim: true,
                index: true // Enables queries like: find all documents for a specific user
            },
            sName: {
                type: String,
                required: [true, "Document name is required"],
                trim: true,
                index: true // Indexed for faster search and filtering by document name
            },
            tManageDoc: {
                type: Schema.Types.ObjectId,
                ref: 'manageDocument',
                trim: true,
                index: true // Indexed for faster lookups and joins by document type
            },
            tDocumentDetails: {
                type: [UserDocument._documentDetailsSchema]
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true, // Adds createdAt and updatedAt fields automatically
            versionKey: false // Disables __v field to reduce document size
        }
    );

    // Compound indexes for optimizing common query patterns

    // Index for organization-document type queries
    // Optimizes queries like:
    // 1. Find all contracts in organization X
    // 2. Count documents by type in organization
    static {
        UserDocument._schema.index({ tOrganization: 1, tManageDoc: 1 });
        
        // Text index enables natural language search across document names and URLs
        // Example: await UserDocument.find({ $text: { $search: "employee contract 2025" } })
        UserDocument._schema.index(
            { 
                sName: 'text',
                'tDocumentDetails.sName': 'text',
                'tDocumentDetails.sUrl': 'text'
            },
            {
                weights: {
                    sName: 3, // Main document name has highest priority
                    'tDocumentDetails.sName': 2, // Individual file names second
                    'tDocumentDetails.sUrl': 1 // URLs lowest priority
                },
                name: 'document_text_index'
            }
        );

        // Index for user's documents within an organization
        // Optimizes queries like: find all of user X's documents in organization Y
        UserDocument._schema.index({ tIdUser: 1, tOrganization: 1 });
    }

    /**
     * Mongoose model for UserDocument
     * @private
     */
    private static _model: Model<IUserDocuments> = models?.userDocument || model<IUserDocuments>('userDocument', UserDocument._schema);

    /**
     * Get the Mongoose model for UserDocument
     * @returns {Model<IUserDocuments>} The UserDocument model
     */
    public static get model(): Model<IUserDocuments> {
        return UserDocument._model;
    }
}