import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import {
  Theme,
  ThemeService,
} from '../../../../../src/app/theme-service.service';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenav } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import {MatMenuModule} from '@angular/material/menu';
import {MatListModule} from '@angular/material/list';
import {MatDividerModule} from '@angular/material/divider';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ProfileMenuComponent } from './profile-menu/profile-menu.component';
@Component({
  selector: 'lib-navbar',
  standalone: true,
  imports: [MatToolbarModule,MatIconModule, MatButtonModule,MatMenuModule,MatListModule,MatDividerModule,MatSlideToggleModule,ProfileMenuComponent],
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.css',
})
export class NavbarComponent {
 
  @Input() sidebarRef: MatSidenav | undefined;
  @Input() theme: Theme = Theme.Light;
  @ViewChild(ProfileMenuComponent , { static: true }) sMenu!: ProfileMenuComponent;

  @Output() clickMenuOption = new EventEmitter<string>();

  constructor(private themeService: ThemeService) {
  }

  toggleTheme() {
    this.themeService.setTheme(
      this.theme === Theme.Light ? Theme.Dark : Theme.Light
    );
    
  }

  onMenuOptionClick(route:string) {
    this.clickMenuOption.emit(route);
  }
}
