import { model, Model, models, Schema } from "mongoose";
import { IPayslip } from "../../interfaces/organization/payslip.interface";
import { EMSSalaryStructureCategory } from "../../../../utils/meta/enum.utils";
import { ISalaryStructureTemplate } from "../../interfaces/organization/salaryStructure.interface";
import { PayslipSchemas } from "../shared/payslip-schemas";

/**
 * Payslip model class that defines the schema and provides access to the Payslip collection.
 * 
 * @class Payslip
 * @description Handles the schema definition and model creation for Payslips
 */
export default class Payslip {
    /**
     * Mongoose schema definition for SalaryStructureTemplate
     * @private
     */
    private static _salaryStructureTemplateSchema = new Schema<ISalaryStructureTemplate>({
        tSalaryStructure: {
            type: Schema.Types.Mixed
        },
        eType: {
            type: String,
            enum: EMSSalaryStructureCategory
        }
    }, { _id: false });

    /**
     * Mongoose schema definition for Payslip
     * @private
     */
    private static _schema = new Schema<IPayslip>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                required: [true, "User ID is required"],
                trim: true,
                ref: 'login',
                index: true // Indexed for faster lookups by user
            },
            aMonth: {
                type: Number,
                required: [true, "Month is required"],
                min: [1, "Month must be between 1 and 12"],
                max: [12, "Month must be between 1 and 12"],
                trim: true,
                index: true // Indexed for filtering by month
            },
            aYear: {
                type: Number,
                required: [true, "Year is required"],
                min: [1900, "Invalid year"],
                max: [9999, "Invalid year"],
                trim: true,
                index: true // Indexed for filtering by year
            },
            aSalaryDay: {
                type: Number,
                trim: true,
                min: [1, "Salary day must be between 1 and 31"],
                max: [31, "Salary day must be between 1 and 31"]
            },
            dStartDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            dEndDate: {
                type: Date,
                index: true // Indexed for date-based queries
            },
            tSalaryStructure: {
                type: Schema.Types.Mixed
            },
            eType: {
                type: String,
                enum: EMSSalaryStructureCategory,
                index: true // Indexed for filtering by salary structure type
            },
            bIsReportingAuthApproved: {
                type: Boolean,
                default: false,
                index: true // Indexed for filtering by approval status
            },
            bIsHrApproved: {
                type: Boolean,
                default: false,
                index: true // Indexed for filtering by HR approval status
            },
            aMiscAmount: {
                type: Number,
                default: 0
            },
            aMiscDescription: {
                type: String,
                trim: true
            },
            user: {
                type: PayslipSchemas.userSchema
            },
            userBank: {
                type: PayslipSchemas.userBankSchema
            },
            leaveBalance: {
                type: [PayslipSchemas.leaveBalanceSchema],
                default: []
            },
            salaryStructureTemplate: {
                type: Payslip._salaryStructureTemplateSchema
            }
        },
        PayslipSchemas.basePayslipConfig
    );

    /**
     * Mongoose model for Payslip
     * @private
     */
    private static _model: Model<IPayslip> = models?.Payslip || model<IPayslip>("payslip", Payslip._schema);

    /**
     * Get the Mongoose model for Payslip
     * @returns {Model<IPayslip>} The Payslip model
     */
    public static get model(): Model<IPayslip> {
        return Payslip._model;
    }
}