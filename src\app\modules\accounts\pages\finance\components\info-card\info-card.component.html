<p-card class="shadow-md rounded-2xl bg-white">
  <div class="text-[#418083] text-[15px] font-semibold">
    {{ data()?.title }}
  </div>
  <div
    class="grid grid-cols-3 text-[14px] gap-2 space-y-4 items-end"
  >
    <div class="hidden"></div>
    <div *ngFor="let item of data()?.values" class="scale-90">
      <span class="font-medium text-[#799dad]">{{ item.label }}</span
      ><br />
      <span class="font-semibold text-[{{ item.valueHexColor }}]">{{
        item.value
      }}</span>
    </div>
  </div>
</p-card>
