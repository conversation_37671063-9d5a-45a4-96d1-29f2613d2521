<p-card>
  <div class="flex justify-between items-center mb-2">
    <div class="flex items-center gap-2">
      <h1 class="text-[17px] font-semibold">
        {{ "dashboard.request.title" | translate }}
      </h1>
      <span
        >{{ "(" }} {{ "dashboard.request.lastFourRecords" | translate }}
        {{ ")" }}</span
      >
    </div>
    <p-button
      icon="pi pi-plus"
      [size]="'small'"
      rounded="true"
      class="scale-90"
    />
  </div>

  <p-table
    [value]="requests"
    styleClass="text-[10px] h-[158px] overflow-y-auto"
    [size]="'small'"
  >
    <ng-template #header>
      <tr>
        <th scope="col">Dates</th>
        <th class="whitespace-nowrap" scope="col">
          {{ "dashboard.request.type" | translate }}
        </th>
        <th scope="col">Status</th>
        <th scope="col"></th>
      </tr>
    </ng-template>
    <ng-template #body let-request>
      <tr>
        <td class="whitespace-nowrap">
          <div class="flex overflow-x-auto gap-1 hide-scrollbar">
            <p
              class="bg-[var(--p-primary-200)] py-[2px] px-2 text-center text-[var(--p-primary-900)] rounded-3xl shrink-0"
            >
              {{ request.dDate[0] | date : "d MMM" }}
            </p>
            <span *ngIf="request.dDate.length > 1">
              + {{ request.dDate.length - 1 }}
            </span>
          </div>
        </td>

        <td>{{ request.eType | funcRunner : getRequestType }}</td>
        <td>
          <span [class]="request.eStatus | funcRunner : getStatusSeverity">
            {{ request.eStatus | funcRunner : getRequestStatus }}
          </span>
        </td>
        <td (click)="viewRequestDetails(request)">
          <span
            class="pi pi-eye scale-75 text-[var(--p-primary-400)] hover:text-[var(--p-primary-600)] cursor-pointer"
          ></span>
        </td>
      </tr>
    </ng-template>
  </p-table>
</p-card>
