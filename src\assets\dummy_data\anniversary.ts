import { IAnniversary } from 'src/app/modules/home/<USER>/dashboard/models/anniversary';

export const ANNIVERSARY_DATA: IAnniversary[] = [
  {
    user: {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    anniversaryDate: new Date(),
  },
  {
    user: {
      firstName: '<PERSON><PERSON>',
      lastName: '<PERSON>',
      image: 'https://randomuser.me/api/portraits/women/2.jpg',
    },
    anniversaryDate: new Date(),
  },
  {
    user: {
      firstName: 'Deepak',
      lastName: 'Verma',
      image: 'https://randomuser.me/api/portraits/men/3.jpg',
    },
    anniversaryDate: new Date(new Date().setDate(new Date().getDate() + 1)),
  },
  {
    user: {
      firstName: 'Priya',
      lastName: 'Mehta',
      image: 'https://randomuser.me/api/portraits/women/4.jpg',
    },
    anniversaryDate: new Date(new Date().setDate(new Date().getDate() + 2)),
  },
  {
    user: {
      firstName: '<PERSON>an',
      lastName: '<PERSON>',
      image: 'https://randomuser.me/api/portraits/men/5.jpg',
    },
    anniversaryDate: new Date(new Date().setDate(new Date().getDate() + 3)),
  },
  {
    user: {
      firstName: 'Kavita',
      lastName: 'Joshi',
      image: 'https://randomuser.me/api/portraits/women/6.jpg',
    },
    anniversaryDate: new Date(new Date().setDate(new Date().getDate() + 4)),
  },
  {
    user: {
      firstName: 'Rahul',
      lastName: 'Sharma',
      image: 'https://randomuser.me/api/portraits/men/7.jpg',
    },
    anniversaryDate: new Date(new Date().setDate(new Date().getDate() + 5)),
  },
  {
    user: {
      firstName: 'Neha',
      lastName: 'Patel',
      image: 'https://randomuser.me/api/portraits/women/8.jpg',
    },
    anniversaryDate: new Date(new Date().setDate(new Date().getDate() + 6)),
  },
];
