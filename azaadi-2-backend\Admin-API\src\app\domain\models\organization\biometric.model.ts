import { model, Model, models, Schema } from "mongoose";
import { IBiometric } from "../../interfaces/organization/biometric.interface";

/**
 * Biometric model class that defines the schema and provides access to the Biometric collection.
 * 
 * @class Biometric
 * @description Handles the schema definition and model creation for Biometric data
 */
export default class Biometric {
    /**
     * Mongoose schema definition for Biometric
     * @private
     */
    private static _schema: Schema<IBiometric> = new Schema<IBiometric>(
        {
            txnId: {
                type: String,
            },
            dvcId: {
                type: String,
                trim: true,
            },
            dvcIP: {
                type: String,
                trim: true,
            },
            txnDateTime: {
                type: String,
                trim: true,
            },
            punchId: {
                type: String,
                trim: true,
            },
            mode: {
                type: String,
                trim: true,
            },
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for Biometric
     * @private
     */
    private static _model: Model<IBiometric> = models?.Biometric || model<IBiometric>("biometric", Biometric._schema);

    /**
     * Get the Mongoose model for Biometric
     * @returns {Model<IBiometric>} The Biometric model
     */
    public static get model(): Model<IBiometric> {
        return Biometric._model;
    }
}