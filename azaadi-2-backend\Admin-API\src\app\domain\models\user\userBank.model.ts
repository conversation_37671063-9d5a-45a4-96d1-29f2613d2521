import { Schema, model, Model, models } from 'mongoose';
import { IUserBankDetails } from '../../interfaces/user/user-document.interface';

/**
 * UserBank model class that defines the schema and provides access to the UserBank collection.
 * 
 * @class UserBank
 * @description Handles the schema definition and model creation for User Bank Details
 */
export default class UserBank {
    /**
     * Mongoose schema definition for UserBank
     * @private
     */
    private static _schema: Schema<IUserBankDetails> = new Schema<IUserBankDetails>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                required: [true, "Id user must be required!"],
                ref: 'login',
                trim: true,
                index: true // Indexed for faster lookups by user
            },
            sBankName: {
                type: String,
                trim: true,
                index: true // Indexed for searching by bank name
            },
            sBankAccount: {
                type: String,
                trim: true,
                index: true // Indexed for searching by account number
            },
            sIfscCode: {
                type: String,
                trim: true,
                index: true // Indexed for searching by IFSC code
            },
            sBranchName: {
                type: String,
                trim: true
            },
            bIsActive: {
                type: Boolean,
                default: true,
                index: true // Indexed for filtering active bank accounts
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for UserBank
     * @private
     */
    private static _model: Model<IUserBankDetails> = models?.UserBank || model<IUserBankDetails>("bankDetails", UserBank._schema);

    /**
     * Get the Mongoose model for UserBank
     * @returns {Model<IUserBankDetails>} The UserBank model
     */
    public static get model(): Model<IUserBankDetails> {
        return UserBank._model;
    }
}