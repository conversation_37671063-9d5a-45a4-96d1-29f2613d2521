import { Schema, model, Model, models, Types } from 'mongoose';
import { ILocationTracker, IUserAttendance } from '../../interfaces/user/attendance.interface';
import { EMSAttendanceType, EMSAttendanceStatus } from '../../../../utils/meta/enum.utils';

/**
 * Attendance model class that defines the schema and provides access to the Attendance collection.
 * Handles user attendance records with location tracking and shift management.
 * 
 * @class Attendance
 * @description Handles the schema definition and model creation for User Attendance
  */
export default class Attendance {
    /**
     * Mongoose schema definition for Location Tracker
     * Used to track user's location during attendance
     * 
     * @private
     */
    private static _locationTrackerSchema: Schema<ILocationTracker> = new Schema<ILocationTracker>({
        sLongitude: {
            type: String,
            trim: true
        },
        sLatitude: {
            type: String,
            trim: true
        },
        sTimestamp: {
            type: String,
            trim: true,
            index: true // Indexed for querying location history by time
        },
        sLocation: {
            type: String,
            trim: true,
            index: true // Indexed for location-based filtering
        }
    });

    /**
     * Mongoose schema definition for User Attendance
     * Core schema for tracking employee attendance with location and shift details
     * 
     * @private
     */
    private static _schema: Schema<IUserAttendance> = new Schema<IUserAttendance>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                required: [true, "Id user must be required!"],
                ref: 'login',
                trim: true,
                index: true // Index for user-based queries
            },
            dStartDate: {
                type: [Date],
                required: [true, "Start date must be required!"],
                trim: true,
                index: true // Index for date range queries
            },
            dEndDate: {
                type: [Date],
                required: [true, "End date must be required!"],
                trim: true,
                index: true // Index for calculating attendance duration
            },
            eAttendanceType: {
                type: String,
                enum: EMSAttendanceType,
                default: EMSAttendanceType.OFFLINE,
                index: true // Index for filtering by attendance type
            },
            eEndAttendanceType: {
                type: String,
                enum: EMSAttendanceType,
                default: EMSAttendanceType.OFFLINE,
                index: true // Index for analyzing attendance type changes
            },
            tShift: {
                type: Schema.Types.ObjectId,
                ref: 'shift',
                index: true // Index for shift-based analysis
            },
            eStatus: {
                type: String,
                enum: EMSAttendanceStatus,
                default: EMSAttendanceStatus.ERROR,
                index: true // Index for status-based filtering
            },
            aLateDuration: {
                type: Number,
                default: 0,
                index: true // Index for late arrival reports
            },
            sWorkingHours: {
                type: String,
                default: '00:00'
            },
            tActivityTracker: [{
                type: this._locationTrackerSchema
            }]
        },
        {
            timestamps: true, // Adds createdAt and updatedAt timestamps
            versionKey: false // Disable __v field since we don't need versioning for attendance
        }
    );

    // Add compound indexes for optimizing common query patterns
    static {
        // Index for user-date based queries - optimization for date range queries per user
        // Example: Get user's attendance between dates
        // await Attendance.model.find({
        //   tIdUser: userId,
        //   dStartDate: { $gte: startDate },
        //   dEndDate: { $lte: endDate }
        // })
        Attendance._schema.index({ tIdUser: 1, dStartDate: 1 });
        Attendance._schema.index({ tIdUser: 1, dEndDate: 1 });
        
        // Index for user-shift queries - optimization for shift management
        // Example: Find users in specific shift
        // await Attendance.model.find({
        //   tShift: shiftId
        // }).sort({ tIdUser: 1 })
        Attendance._schema.index({ tIdUser: 1, tShift: 1 });
        
        // Index for status-date queries - optimization for attendance reports
        // Example: Get today's attendance status
        // await Attendance.model.find({
        //   eStatus: EMSAttendanceStatus.FULL_DAY,
        //   dStartDate: { $gte: todayStart, $lte: todayEnd }
        // })
        Attendance._schema.index({ eStatus: 1, dStartDate: 1 });
    }

    /**
     * Gets the Mongoose model for User Attendance
     * @returns The Mongoose model for User Attendance
     */
    public static get model(): Model<IUserAttendance> {
        return models.attendance || model('attendance', this._schema);
    }
}