<div class="overflow-auto h-[calc(100vh-9rem)] px-4 bg-[var(--p-card-background)] rounded-lg">
  <div class="flex justify-between items-center">
    <div class="my-4">
      <h2 class="text-[18px] font-[500]">{{ ticket().title }}</h2>
      <div class="flex gap-4 mt-4">
        <p-tag
          [value]="ticket().status | titlecase"
          [severity]="ticket().status === 'OPEN' ? 'danger': 'success'"
          styleClass="text-[10px]"
          rounded="true"
        />
        <p-tag
          [value]="ticket().priority | titlecase"
          [severity]="ticket().priority | ticketPriorityColor"
          styleClass="text-[10px]"
          rounded="true"
        />
        <p-tag
          [value]="ticket().department | titlecase"
          [severity]="ticket().department | departmentColor"
          styleClass="text-[10px]"
          rounded="true"
        />
      </div>
    </div>
     <p-button label="Raise a Ticket" variant="outlined" rounded="true" />
  </div>
  <p-divider layout="horizontal" />
  <div class="flex gap-4">
    <img
      [src]="ticket().assignedTo.avatar"
      class="h-12 w-12 rounded-full object-cover"
      alt=""
    />
    <div>
      <div class="mb-4">
        <p class="text-[20px] font-[600]">
          {{ ticket().assignedTo.firstName }}
          {{ ticket().assignedTo.lastName }}
        </p>
        <p class="text-[13px] font-[600]">
          {{ ticket().createdAt | date : "dd.MM.YYYY" }}
        </p>
      </div>
      <p >{{ ticket().description }}</p>

      <div *ngIf="ticket().attachments.length > 0" class="mt-8">
        <p class="text-[18px] font-[500]">{{ ticket().attachments.length }} Attachments</p>
        <div class="flex gap-4 mt-2">
          <ng-container *ngFor="let item of ticket().attachments">
            <app-file-down-btn [item]="item" />
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>
