import { Component, Input } from '@angular/core';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { SkeletonModule } from 'primeng/skeleton';
import { PanelModule } from 'primeng/panel';
import { DividerModule } from 'primeng/divider';
import { CardModule } from 'primeng/card';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { IColumn } from '../../models/Column';
import { IAction } from '../../models/Action';

@Component({
  selector: 'app-table',
  imports: [
    TableModule,
    CommonModule,
    SkeletonModule,
    PanelModule,
    DividerModule,
    CardModule,
    IconFieldModule,
    InputIconModule,
    InputTextModule,
    ButtonModule,
    MenuModule,
  ],
  templateUrl: './table.component.html',
})
export class TableComponent<T extends object> {
  @Input() data: T[] = [];
  @Input() cols: IColumn[] = [];
  @Input() isLoading = true;
  @Input() actions: IAction[] = [];

  nums = [1, 2, 3, 4, 5, 6];
}
