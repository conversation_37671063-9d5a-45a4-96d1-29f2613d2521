import { Routes } from '@angular/router';

export const HOME_ROUTES: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'dashboard',
  },
  {
    path: 'dashboard',
    loadComponent: () =>
      import('../home/<USER>/dashboard/dashboard.component').then(
        (m) => m.DashboardComponent
      ),
  },
  {
    path: 'employee',
    loadComponent: () =>
      import('../home/<USER>/employee/employee.component').then(
        (m) => m.EmployeeComponent
      ),
  },
  {
    path: 'ticket',
    loadComponent: () =>
      import('../home/<USER>/ticket/ticket.component').then(
        (m) => m.TicketComponent
      ),
  },
];
