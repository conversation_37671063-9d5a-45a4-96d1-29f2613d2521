<!-- Circular Progress -->
<div class="relative" [style.width.px]="size()" [style.height.px]="size()">
  <svg class="size-full -rotate-90" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
    <!-- Background Circle -->
    <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-[var(--p-primary-100)]" [attr.stroke-width]="thickness()"></circle>
    <!-- Progress Circle -->
    <circle cx="18" cy="18" r="16" fill="none" [ngClass]="'stroke-current ' + color()"  [attr.stroke-width]="thickness()" stroke-dasharray="100" [attr.stroke-dashoffset]="offsetPrgress()" stroke-linecap="round"></circle>
  </svg>

  <!-- Percentage Text -->
  <div class="absolute top-1/2 start-1/2 transform -translate-y-1/2 -translate-x-1/2 flex flex-col items-center justify-between">
    <span class="text-center text-[20px]">{{label()}}</span>
    <span *ngIf="subLabel" class="text-sm text-center"> {{subLabel()}} </span>
  </div>
</div>