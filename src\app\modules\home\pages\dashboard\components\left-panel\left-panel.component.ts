import { CommonModule } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IHoliday, IUser } from '@shared/models';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DrawerModule } from 'primeng/drawer';
import { InputTextModule } from 'primeng/inputtext';
import { PanelModule } from 'primeng/panel';
import { IMessage } from '../../models/Message';
import { IQuickLink } from '../../models/QuickLInk';
import { AttendanceReportComponent } from './components/attendance-report/attendance-report.component';
import { LeaveBalancesComponent } from './components/leave-balances/leave-balances.component';
import { MyAssetsComponent } from './components/my-assets/my-assets.component';
import { RequestComponent } from './components/request/request.component';
import { TeamAvailibilityComponent } from './components/team-availibility/team-availibility.component';
import { TimetrackerComponent } from './components/timetracker/timetracker.component';

@Component({
  selector: 'app-left-panel',
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    ButtonModule,
    DividerModule,
    DrawerModule,
    RouterModule,
    DialogModule,
    PanelModule,
    TimetrackerComponent,
    MyAssetsComponent,
    LeaveBalancesComponent,
    TeamAvailibilityComponent,
    AttendanceReportComponent,
    RequestComponent,
    InputTextModule,
  ],
  templateUrl: './left-panel.component.html',
  styleUrl: './left-panel.component.scss',
})
export class LeftPanelComponent implements OnInit, OnDestroy {
  isWorkFromHomeDrawerVisible = false;
  isHolidayDialogVisible = false;
  quickLinks: IQuickLink[] = [
    {
      label: 'Announcements',
      link: '#',
    },
    {
      label: 'Request',
      link: '#',
    },
    {
      label: 'Organization',
      link: '#',
    },
    {
      label: 'Branches',
      link: '#',
    },
  ];
  onLeave: IUser[] = [
    {
      name: 'John Doe',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'Jane Smith',
      image:
        'https://media.istockphoto.com/id/1437816897/photo/business-woman-manager-or-human-resources-portrait-for-career-success-company-we-are-hiring.jpg?s=612x612&w=0&k=20&c=tyLvtzutRh22j9GqSGI33Z4HpIwv9vL_MZw_xOE19NQ=',
    },
    {
      name: 'Bob Johnson',
      image:
        'https://plus.unsplash.com/premium_photo-1689533448099-2dc408030f0f?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDMxfHx8ZW58MHx8fHx8',
    },
    {
      name: 'Alice Williams',
      image:
        'https://plus.unsplash.com/premium_photo-1690294614341-cf346ba0a637?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDM0fHx8ZW58MHx8fHx8',
    },
    {
      name: 'Charlie Brown',
      image:
        'https://plus.unsplash.com/premium_photo-1688350808212-4e6908a03925?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDI4fHx8ZW58MHx8fHx8',
    },
    {
      name: 'Eve Green',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'George White',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'Eve Green',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
    {
      name: 'George White',
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
    },
  ];
  workingRemotely: IUser[] = [
    {
      name: 'Jane Smith',
      image:
        'https://plus.unsplash.com/premium_photo-1693000696693-26aa43e8b97e?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDR8fHxlbnwwfHx8fHw%3D',
    },
    {
      name: 'Charlie Brown',
      image:
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
    {
      name: 'Chandan Sahw',
      image:
        'https://plus.unsplash.com/premium_photo-1693258698597-1b2b1bf943cc?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDE4fHx8ZW58MHx8fHx8',
    },
    {
      name: 'Ruchika',
      image:
        'https://plus.unsplash.com/premium_photo-1690487729258-7dcefc84a532?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDh8fHxlbnwwfHx8fHw%3D',
    },
    {
      name: 'Adrija',
      image:
        'https://plus.unsplash.com/premium_photo-1690395794791-e85944b25c0f?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
    {
      name: 'Jane Smith',
      image:
        'https://plus.unsplash.com/premium_photo-1693000696693-26aa43e8b97e?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDR8fHxlbnwwfHx8fHw%3D',
    },
    {
      name: 'Charlie Brown',
      image:
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
  ];
  messages: IMessage[] = [
    {
      sender: {
        name: 'Chandan Shaw',
        image:
          'https://www.shutterstock.com/image-photo/head-shot-portrait-close-smiling-600nw-1714666150.jpg',
      },
      content:
        'Hello gys how are you. This is a post regarding the fun friday. Anyone who want to participate please comment p',
      date: new Date(),
    },
    {
      sender: {
        name: 'Rahul Das',
        image:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      },
      content:
        'Hello gys how are you. This is a post regarding the fun friday. Anyone who want to participate please comment p',
      date: new Date(),
    },
    {
      sender: {
        name: 'John Doe',
        image:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      },
      content:
        'Hello gys how are you. This is a post regarding the fun friday. Anyone who want to participate please comment p',
      date: new Date(),
    },
  ];
  carouselItems: IHoliday[] = [
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 1',
      description: 'Description 1',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 2',
      description: 'Description 2',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 3',
      description: 'Description 3',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 4',
      description: 'Description 4',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 5',
      description: 'Description 5',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 6',
      description: 'Description 6',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 7',
      description: 'Description 7',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 8',
      description: 'Description 8',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 9',
      description: 'Description 9',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 10',
      description: 'Description 10',
      date: new Date(),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 11',
      description: 'Description 11',
      date: new Date('Fri, 23 November, 2025'),
    },
    {
      image:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT_B8cYF8ZVSmK8ey4GWZx3BSiDAEUDqR8kiLJjQsXjUV2865K8WVq5PYNSHoMpVWlTetU&usqp=CAU',
      title: 'Title 12',
      description: 'Description 12',
      date: new Date('Sat, 23 December, 2025'), // 'Sat, 23 December, 2025',
    },
  ];
  leaveBalance = [
    { name: 'Casual Leave', balance: 12 },
    { name: 'Sick Leave', balance: 4 },
    { name: 'Other Leave', balance: 7 },
  ];

  currentDate = new Date();
  private intervalId: any;

  ngOnInit(): void {
    this.intervalId = setInterval(() => {
      this.currentDate = new Date();
    }, 1000);
  }

  ngOnDestroy(): void {
    if (this.intervalId) clearInterval(this.intervalId);
  }

  getColor(date: Date): string {
    let month = date.getMonth();
    switch (month) {
      case 0:
        return '#D32F2F'; // Medium Dark Red
      case 1:
        return '#F9A825'; // Medium Dark Yellow
      case 2:
        return '#388E3C'; // Medium Dark Green
      case 3:
        return '#1976D2'; // Medium Dark Blue
      case 4:
        return '#3949AB'; // Medium Dark Indigo
      case 5:
        return '#D81B60'; // Medium Dark Pink
      case 6:
        return '#00796B'; // Medium Dark Teal
      case 7:
        return '#C0CA33'; // Medium Dark Lime
      case 8:
        return '#0097A7'; // Medium Dark Cyan
      case 9:
        return '#FB8C00'; // Medium Dark Orange
      case 10:
        return '#FFA000'; // Medium Dark Amber
      case 11:
        return '#8E24AA'; // Medium Dark Violet
      default:
        return '#757575'; // Medium Dark Gray
    }
  }

  isHolidayPassed(date: Date): boolean {
    let today = new Date();
    return date < today;
  }

  closeWorkFromHomeDrawer() {
    this.isWorkFromHomeDrawerVisible = false;
  }
}
