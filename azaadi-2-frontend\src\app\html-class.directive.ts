import {
  Directive,
  ElementRef,
  Renderer2,
  Input,
  OnChanges,
} from '@angular/core';
import { Theme } from './theme-service.service';

@Directive({
  selector: '[appHtmlClass]',
  standalone: true,
})
export class HtmlClassDirective implements OnChanges {
  @Input() defaultColor: string = '';
  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnChanges() {
    const hostElem = this.el.nativeElement.parentNode.parentNode;
    this.renderer.removeClass(
      hostElem,
      this.defaultColor == Theme.Light ? Theme.Dark : Theme.Light
    );
    this.renderer.addClass(hostElem, this.defaultColor);
  }
}
