import { model, Model, models, Schema } from "mongoose";
import { IDifferentSalarySheet } from "../../interfaces/organization/salaryReport.interface";

/**
 * SalaryDifferentSheet model class that defines the schema and provides access to the SalaryDifferentSheet collection.
 * 
 * @class SalaryDifferentSheet
 * @description Handles the schema definition and model creation for Salary Different Sheets
 */
export default class SalaryDifferentSheet {
    /**
     * Mongoose schema definition for SalaryDifferentSheet
     * @private
     */
    private static _schema = new Schema<IDifferentSalarySheet>(
        {
            aMonth: {
                type: Number,
                required: [true, "Month is missing"],
                min: [1, "Month must be between 1 and 12"],
                max: [12, "Month must be between 1 and 12"],
                index: true // Indexed for filtering by month
            },
            aYear: {
                type: Number,
                required: [true, "Year is missing"],
                min: [1900, "Invalid year"],
                max: [9999, "Invalid year"],
                index: true // Indexed for filtering by year
            },
            aTotalAmount: {
                type: Number,
                required: [true, "Total amount is missing"]
            },
            aAmountDifference: {
                type: Number
            },
            aTotalEmployees: {
                type: Number
            },
            tEmployeesJoin: {
                type: [Schema.Types.ObjectId],
                ref: "login"
            },
            tEmployeesLeft: {
                type: [Schema.Types.ObjectId],
                ref: "login"
            },
            tApprovedBy: {
                type: Schema.Types.ObjectId,
                ref: "login",
                trim: true,
                index: true // Indexed for filtering by approver
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                required: [true, "Organization is required"],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for SalaryDifferentSheet
     * @private
     */
    private static _model: Model<IDifferentSalarySheet> = models?.SalaryDifferentSheet || model<IDifferentSalarySheet>("differentsalarysheet", SalaryDifferentSheet._schema);

    /**
     * Get the Mongoose model for SalaryDifferentSheet
     * @returns {Model<IDifferentSalarySheet>} The SalaryDifferentSheet model
     */
    public static get model(): Model<IDifferentSalarySheet> {
        return SalaryDifferentSheet._model;
    }
}