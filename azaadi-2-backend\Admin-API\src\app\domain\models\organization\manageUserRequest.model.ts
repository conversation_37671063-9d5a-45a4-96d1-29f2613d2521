import { Model, model, models, Schema } from "mongoose";
import { IManageUserRequest } from "../../interfaces/organization/manageRequest.interface";

/**
 * ManageUserRequest model class that defines the schema and provides access to the ManageUserRequest collection.
 * 
 * @class ManageUserRequest
 * @description Handles the schema definition and model creation for User Management Requests
 */
export default class ManageUserRequest{
    /**
     * Mongoose schema defination for ManageUserRequest
     * @private
     */
    private static _schema:Schema<IManageUserRequest> = new Schema<IManageUserRequest>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                ref: "user",
                required: [true, "User is required"],
                trim: true,
                index: true // Indexed for faster lookups and joins by user
            },
            tType: {
                type: Schema.Types.ObjectId,
                ref: "managerolerequest",
                required: [true, "Type is required"],
                trim: true,
                index: true // Indexed for faster lookups and joins by role request type
            },
            aCount: {
                type: Number,
                required: [true, "Count is required"]
            },
            sDescription: {
                type: String,
                trim: true
            },
            tRole: {
                type: Schema.Types.ObjectId,
                ref: "role",
                trim: true,
                index: true // Indexed for faster lookups and joins by role
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for ManageUserRequest
     * @private
     */
    private static _model: Model<IManageUserRequest> = models?.manageuserrequest || model<IManageUserRequest>("manageuserrequest", ManageUserRequest._schema);

    /**
     * Get the Mongoose model for ManageUserRequest
     * @returns {Model<IManageUserRequest>} The ManageUserRequest model
     */
    public static get model(): Model<IManageUserRequest> {
        return ManageUserRequest._model;
    }
}