import { Types } from 'mongoose';
import { IConcept } from '../concept.interface';
import { EmsActivityLogEntityType } from '../../../../utils/meta/enum.utils';

export interface IManageLog extends IConcept {
    tIdUser: string | Types.ObjectId;
    sMetaKey: string[];
    sMetaValue: string[];
}

export interface ISuperUserActivityLog extends IConcept {
    sEntityType: EmsActivityLogEntityType;
    tCreatedBy: string | Types.ObjectId;
    tEntityIds: Types.ObjectId[];
}