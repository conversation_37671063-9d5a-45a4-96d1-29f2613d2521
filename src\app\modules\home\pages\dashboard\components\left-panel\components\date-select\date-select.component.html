<ng-container *ngIf="activeView === 'month'">
  <div class="flex justify-between items-center mb-2">
    <p-button variant="text" (click)="this.activeView = 'year'">
      {{ selectedYear() }}
    </p-button>
    <div class="flex gap-2">
      <p-button
        icon="pi pi-chevron-left"
        variant="text"
        (click)="previousYear()"
      ></p-button>

      <p-button
        icon="pi pi-chevron-right"
        variant="text"
        (click)="nextYear()"
      ></p-button>
    </div>
  </div>

  <div class="grid grid-cols-3 place-items-center">
    <p-button
      variant="text"
      severity="secondary"
      rounded="true"
      *ngFor="let month of months; let i = index"
      class="mx-1"
      (click)="selectMonth(i)"
    >
      {{ month }}
    </p-button>
  </div>
</ng-container>

<!-- Year View -->
<ng-container *ngIf="activeView === 'year'">
  <div class="flex justify-between items-center mb-2">
    <p-button
      icon="pi pi-chevron-left"
      variant="text"
      (click)="previousYearsRange()"
    ></p-button>
    <p-button variant="text" (click)="this.activeView = 'month'">
      {{ years[0] }} - {{ years[years.length - 1] }}
    </p-button>
    <p-button
      icon="pi pi-chevron-right"
      variant="text"
      (click)="nextYearsRange()"
    ></p-button>
  </div>

  <div class="grid grid-cols-3 place-items-center">
    <p-button
      variant="text"
      severity="secondary"
      rounded="true"
      *ngFor="let year of years"
      class="mx-1"
      (click)="selectYear(year)"
    >
      {{ year }}
    </p-button>
  </div>
</ng-container>
