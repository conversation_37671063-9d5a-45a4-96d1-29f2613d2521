import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import {
  AttendanceType,
  ITodaysAttendanceResponse,
} from '../../models/Attendance';

@Injectable()
export class AttendanceApiService {
  private readonly _http = inject(HttpClient);
  private readonly _baseUrl = environment.apiUrl;
  private readonly _timeTrackerUrl = `${this._baseUrl}/TimeTracker`;

  fetchTimeTrackerData(): Observable<ITodaysAttendanceResponse> {
    const mockData: ITodaysAttendanceResponse = {
      dStartDate: [new Date(new Date().setHours(9, 0, 0))],
      dEndDate: [new Date(new Date().setHours(18, 0, 0))],
      eAttendanceType: AttendanceType.ONLINE,
      eEndAttendanceType: AttendanceType.OFFLINE,
      tShift: 'Day',
    };
    return of(mockData);
  }

  fetchAttendanceData(): Observable<any> {
    return this._http.get<any>(`${this._timeTrackerUrl}/getAttendance`);
  }
}
