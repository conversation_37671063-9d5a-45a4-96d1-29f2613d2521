<p-card>
  <p class="text-2xl ml-4">{{ translatedHeading }}</p>
  <p-breadcrumb class="max-w-full" [model]="translatedItems">
    <ng-template #item let-item>
      <ng-container *ngIf="item.route; else elseBlock">
        <a [routerLink]="item.route" class="p-breadcrumb-item-link">
          <span class="text-primary font-semibold">{{ item.label }}</span>
        </a>
      </ng-container>
      <ng-template #elseBlock>
        <span
          *ngIf="item.icon"
          class="material-symbols-outlined text-xl p-breadcrumb-item-link"
        >
          {{ item.icon }}
        </span>
        <span class="text-primary font-semibold p-breadcrumb-item-link">{{
          item.label
        }}</span>
      </ng-template>
    </ng-template>
  </p-breadcrumb>
</p-card>
