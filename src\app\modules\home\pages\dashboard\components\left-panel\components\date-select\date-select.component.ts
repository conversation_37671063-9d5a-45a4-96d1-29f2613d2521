import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  input,
  Output,
  signal,
} from '@angular/core';
import { YearRangeService } from '@shared/services/year-range.service';
import { ButtonModule } from 'primeng/button';
const MONTHS = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];
@Component({
  selector: 'app-date-select',
  imports: [CommonModule, ButtonModule],
  templateUrl: './date-select.component.html',
  providers: [YearRangeService],
})
export class DateSelectComponent {
  @Output() selectedDateChange = new EventEmitter<Date>();
  currentDate = input<Date>(new Date());
  years: number[] = [];
  selectedYear = signal(this.currentDate().getFullYear());
  selectedMonth = signal(this.currentDate().getMonth());
  activeView: 'month' | 'year' = 'month';
  isMonthSelected: boolean = false;
  isYearSelected: boolean = false;
  months: string[] = MONTHS;
  private readonly yearRangeService = inject(YearRangeService);

  ngOnInit() {
    this.years = this.yearRangeService.getYears();
  }
  previousYearsRange() {
    this.years = this.yearRangeService.previous();
  }

  nextYearsRange() {
    this.years = this.yearRangeService.next();
  }
  resetYearsRange() {
    this.years = this.yearRangeService.reset();
  }

  nextYear() {
    this.selectedYear.update((year) => year + 1);
    this.isYearSelected = true;
  }
  previousYear() {
    this.selectedYear.update((year) => year - 1);
    this.isYearSelected = true;
  }

  selectMonth(month: number) {
    this.selectedMonth.set(month);
    this.emitSelectedDate();
  }
  selectYear(year: number) {
    this.selectedYear.set(year);
    this.isYearSelected = true;
    if (this.isMonthSelected) {
      this.emitSelectedDate();
    } else {
      this.activeView = 'month';
    }
  }

  emitSelectedDate() {
    const date = new Date(this.selectedYear(), this.selectedMonth(), 1);
    this.selectedDateChange.emit(date);
    this.isMonthSelected = false;
    this.isYearSelected = false;
    this.activeView = 'month';
    this.yearRangeService.reset();
  }
}
