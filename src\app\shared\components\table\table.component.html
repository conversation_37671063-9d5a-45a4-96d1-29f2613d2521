<p-table *ngIf="isLoading" [value]="nums">
  <ng-template #header>
    <tr>
      <th *ngFor="let item of nums" scope="col">
        <p-skeleton width="auto" />
      </th>
    </tr>
  </ng-template>
  <ng-template #body let-num>
    <tr>
      <td *ngFor="let item of nums">
        <p-skeleton />
      </td>
    </tr>
  </ng-template>
</p-table>

<p-card>
  <div class="flex gap-4">
    <ng-content></ng-content>
    <div class="ml-auto">
      <p-button
        icon="pi pi-ellipsis-v"
        [rounded]="true"
        outlined="true"
        severity="secondary"
        (onClick)="actionMenu.toggle($event)"
        *ngIf="actions.length > 0"
      />
      <p-menu #actionMenu [popup]="true" [model]="actions" [styleClass]="'p-2'">
        <ng-template let-item pTemplate="item">
          <div class="p-2 rounded-md flex gap-3 items-center cursor-pointer">
            <span [ngClass]="item.icon"></span>
            {{ item.label }}
          </div>
        </ng-template>
      </p-menu>
    </div>
  </div>
  <p-table
    #dt
    [value]="data"
    *ngIf="!isLoading"
    [paginator]="true"
    [rows]="10"
    [rowsPerPageOptions]="[10, 25, 50, 100]"
    showCurrentPageReport="true"
  >
    <ng-template pTemplate="caption"> </ng-template>
    <ng-template pTemplate="header">
      <tr>
        <th scope="col">No.</th>
        <th
          *ngFor="let col of cols"
          pSortableColumn="{{ col.field }}"
          scope="col"
        >
          {{ col.header }} <p-sortIcon field="{{ col.field }}" />
        </th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-row let-i="rowIndex">
      <tr>
        <td>{{ i + 1 }}</td>
        <td class="min-w-fit" *ngFor="let col of cols">
          {{ row[col.field] }}
        </td>
      </tr>
    </ng-template>
  </p-table>
</p-card>
