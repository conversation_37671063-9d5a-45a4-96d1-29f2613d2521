import { model, Model, models, Schema } from "mongoose";
import { IDepartment } from "../../interfaces/organization/department.interface";

/**
 * Department model class that defines the schema and provides access to the Department collection.
 * 
 * @class Department
 * @description Handles the schema definition and model creation for Departments
 */
export default class Department {
    /**
     * Mongoose schema definition for Department
     * @private
     */
    private static _schema = new Schema<IDepartment>(
        {
            sName: {
                type: String,
                required: [true, 'Name is missing!'],
                trim: true,
                index: true // Indexed for faster search and filtering by department name
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                required: [true, 'Organization is missing!'],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            },
            tDepartmentHead: {
                type: Schema.Types.ObjectId,
                ref: 'role',
                trim: true,
                index: true // Indexed for hierarchy-based queries
            },
            sTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by department tag
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for Department
     * @private
     */
    private static _model: Model<IDepartment> = models?.Department || model<IDepartment>("department", Department._schema);

    /**
     * Get the Mongoose model for Department
     * @returns {Model<IDepartment>} The Department model
     */
    public static get model(): Model<IDepartment> {
        return Department._model;
    }
}