import { Component, input } from '@angular/core';
import { IdentityInfo } from '../../models/identity-info';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-identity-info',
  imports: [CardModule,TagModule,CommonModule],
  templateUrl: './identity-info.component.html',
  styleUrl: './identity-info.component.scss'
})
export class IdentityInfoComponent {
  data = input<IdentityInfo[]|null>(null);
}
