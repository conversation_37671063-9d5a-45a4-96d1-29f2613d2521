:root {
  --primary-color: #f87171;
}
.box {
  border: 1px solid rgb(174, 171, 171);
  cursor: pointer;
  margin: 8px 40px;
  position: relative;
  min-width: fit-content;
  display: flex;
  align-items: center;
  border-radius: 50px;
  padding: 4px;
}

.box.active {
  border: 1px solid red;
}

.active-border::before {
  border-left: 4px solid red;
}

.box::before {
  content: "";
  position: absolute;
  left: -20px;
  width: 20px;
  height: calc(100% + 11px);
  top: 50%;
  border-top: 2px solid rgb(208, 197, 197);
  pointer-events: none;
}

.box.active::before {
  border-top: 4px solid red;
  z-index: 100;
}

.child {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 2px solid rgb(208, 197, 197);
  cursor: pointer;
}

.child::before {
  content: "";
  position: absolute;
  left: -18px;
  width: 18px;
  top: 50%;
  border-top: 2px solid rgb(208, 197, 197);
}

.activeChild::after {
  content: "";
  position: absolute;
  height: 100%;
  top: 50%;
  right: -23px;
  width: 23px;
}

.activeChild {
  border-color: red;
  border: 2px solid red;
}

.activeChild::before,
.activeChild::after {
  border-top: 4px solid red;
}

.depth-zero::before {
  border: 0;
}
