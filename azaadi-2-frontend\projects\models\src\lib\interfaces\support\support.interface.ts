import { emsSupportCategory, emsSupportPriority, emsSupportType } from "../../enums/support.enum";
import { ICommon, ICommonDescription, ICommonOrg, ICommonTileValue } from "../common.interface";
import { EmsBranches, EmsOrganization } from "../organization/manageOrganization.interface";
import { EmsUser } from "../users/user.interface";

export interface EmsItSupport extends ICommon,ICommonDescription,SUrl,Partial<ICommonOrg>,Partial<Pick<ICommonTileValue,"sTitle">>{
    aTicketId?: number;
    tRequestBy?: EmsUser | string;
    eType?: emsSupportType;
    tWatchers?: EmsUser[] | string[];
    ePriority?: emsSupportPriority;
    eCategory?: emsSupportCategory;
    tBranch?: EmsBranches | string;
    dRequestDate?: Date;
    bIsSolved?: boolean;
}

export interface EmsSupportChat extends ICommon,SUrl {
    tItSupport?: EmsItSupport | string;
    tSendBy?: EmsUser | string;
    sMessage?: string;
}

interface SUrl{
    sUrl?: string[];
}