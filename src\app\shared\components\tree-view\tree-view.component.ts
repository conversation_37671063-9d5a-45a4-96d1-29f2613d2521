import { CommonModule } from '@angular/common';
import { Component, input, linkedSignal, OnInit, signal } from '@angular/core';

export interface ITreeNode {
  id: number;
  label: string;
  expanded: boolean;
  designation: string;
  children?: ITreeNode[];
  image: string;
  name: string;
}
@Component({
  selector: 'app-tree-view',
  imports: [CommonModule],
  templateUrl: './tree-view.component.html',
  styleUrl: './tree-view.component.scss',
})
export class TreeViewComponent implements OnInit {
  parentIndex = input.required<number>();
  depth = input.required<number>();
  data = input.required<ITreeNode[]>();

  ngOnInit(): void {
    console.log('Parent Index:', this.parentIndex());
    console.log('Depth:', this.depth());
    console.log('Data:', this.data());
  }

  currentNode = linkedSignal <ITreeNode> (() => {
    return this.data()[0];
  })

  startIndex = signal(-1);
  endIndex = signal(-1);
  showName = signal(false);

  updateCurrentActive(node: ITreeNode, index: number) {
    this.calculateStartAndEnd(index);
    this.showName.set(!this.showName());
    console.log('node', node);
    
    if (this.currentNode() === node) {
      node.expanded = !node.expanded;
      if (!node.expanded) {
        this.markFalse(node);
      }
    } else {
      if (this.currentNode) {
        this.markFalse(this.currentNode());
      }
      this.currentNode.set(node);
      node.expanded = true;
    }
  }

  markFalse(node: ITreeNode) {
    node.expanded = false;
    if (node.children) {
      node.children.forEach((child) => {
        this.markFalse(child);
      });
    }
  }

  calculateStartAndEnd(index: number) {
    let childIndex = index;
    if (this.parentIndex() > childIndex) {
      this.startIndex.set(childIndex);
      this.endIndex.set(this.parentIndex());
    } else {
      this.startIndex.set(this.parentIndex());
      this.endIndex.set(childIndex);
    }
  }

  getNodeClasses(node: ITreeNode, i: number) {
    return {
      'depth-zero': this.depth() === 0,
      'show-detail': this.showName,
      'before:border-l-2': i !== this.data.length - 1,
      'after:border-0': !node.children,
      active: node.expanded && this.depth() !== 0,
      'bg-red-400': node.expanded,
      'active-border': this.depth() !== 0 && this.isInTheBoundary(i),
    };
  }

  isInTheBoundary(index: number) {
    return index >= this.startIndex() && index < this.endIndex();
  }

  getNodeBackGroundColor() {
    switch (this.depth() % 3) {
      case 0:
        return 'bg-red-400';
      case 1:
        return 'bg-green-400';
      case 2:
        return 'bg-blue-400';
      default:
        return 'bg-white';
    }
  }
}
