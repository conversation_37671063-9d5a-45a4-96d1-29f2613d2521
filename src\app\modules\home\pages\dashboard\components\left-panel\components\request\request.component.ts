import { Component, inject, OnInit, signal } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import {
  IRequest,
  RequestStatus,
  RequestType,
} from '../../../../models/request';
import { CommonModule, DatePipe } from '@angular/common';
import { TagModule } from 'primeng/tag';
import { TableModule } from 'primeng/table';
import { TranslatePipe } from '@ngx-translate/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ViewRequestDialogComponent } from './view-request-dialog/view-request-dialog.component';
import { FuncRunnerPipe } from '@shared/pipes/func-runner.pipe';
@Component({
  selector: 'app-request',
  imports: [
    CardModule,
    ButtonModule,
    CommonModule,
    TagModule,
    TableModule,
    TranslatePipe,
    FuncRunnerPipe,
  ],
  templateUrl: './request.component.html',
  styleUrls: ['./request.component.scss'],
  providers: [DatePipe, DialogService],
})
export class RequestComponent implements OnInit {
  requests: IRequest[] = [
    {
      dDate: [new Date('2025-05-20')],
      eStatus: RequestStatus.PENDING,
      eType: RequestType.SICK_LEAVE,
      tSender: 'john.doe',
      sReason: 'Fever and cold',
      sMessage:
        " Dear Ma'am/Sir, with due respect, I would like to inform you that I need a full-day sick leave on 19.05.2025, due to some emergency . I would be highly obliged if you could grant me this leave. Thank you for your understanding. Thanks & regards, Puja Bose",
      tApproverBy: 'manager',
    },
    {
      dDate: [new Date('2025-06-10'), new Date('2025-06-11')],
      eStatus: RequestStatus.APPROVED,
      eType: RequestType.CASUAL_LEAVE,
      tSender: 'jane.smith',
      sReason: 'Personal work',
      sMessage: 'Need two days off to attend a family function.',
      tApproverBy: 'supervisor',
    },
    {
      dDate: [new Date('2025-05-22')],
      eStatus: RequestStatus.REJECTED,
      eType: RequestType.WORK_FROM_HOME,
      tSender: 'alice.williams',
      sReason: 'Internet issue at office',
      sMessage:
        'Requesting to work from home due to technical issues at office.',
      tApproverBy: 'teamlead',
    },
    {
      dDate: [new Date('2025-05-25')],
      eStatus: RequestStatus.PENDING,
      eType: RequestType.WORK_FROM_HOME,
      tSender: '<EMAIL>',
      sReason: 'Traveling to hometown',
      sMessage:
        'Will be traveling, requesting work from home access for one day.',
      tApproverBy: '<EMAIL>',
    },
  ];

  private _viewRequestDialogRef: DynamicDialogRef | undefined;
  private readonly _dialogService: DialogService = inject(DialogService);

  // getDateRange(startDate: Date, endDate: Date): string {
  //   const start = new Date(startDate);
  //   const end = new Date(endDate);
  //   const formatedStartDate = new DatePipe('en-US').transform(start, 'dd MMM');
  //   const formatedEndDate = new DatePipe('en-US').transform(end, 'dd MMM');
  //   return `${formatedStartDate} - ${formatedEndDate}`;
  // }

  ngOnInit(): void {}

  viewRequestDetails(request: IRequest): void {
    this._viewRequestDialogRef = this._dialogService.open(
      ViewRequestDialogComponent,
      {
        header: 'Request Details',
        data: request,
        modal: true,
        closable: true,
        dismissableMask: true,
      }
    );
  }

  getRequestStatus(status: number): string {
    console.log(status, 'status');
    switch (status) {
      case RequestStatus.PENDING:
        return 'Pending';
      case RequestStatus.APPROVED:
        return 'Approved';
      case RequestStatus.REJECTED:
        return 'Rejected';
      default:
        return '';
    }
  }

  getRequestType(type: number): string {
    console.log(type, 'type');
    switch (type) {
      case RequestType.SICK_LEAVE:
        return 'Sick Leave';
      case RequestType.CASUAL_LEAVE:
        return 'Casual Leave';
      case RequestType.WORK_FROM_HOME:
        return 'WFH';
      default:
        return '';
    }
  }

  getStatusSeverity(status: number): string {
    console.log(status, 'status');

    switch (status) {
      case RequestStatus.PENDING:
        return 'text-yellow-600';
      case RequestStatus.APPROVED:
        return 'text-green-600';
      case RequestStatus.REJECTED:
        return 'text-red-600';
      default:
        return '';
    }
  }
}
