export enum EmsUserAccess {
  READ,
  WRITE,
  ALL,
}
export enum EmsFileType {
  pdf,
  jpg,
  jpeg,
  doc,
  excel,
  xml,
}

export enum EmsProcessType {
  EXAM,
  INTERVIEW,
  DOCUMENT,
  BACKGROUND,
}

export declare enum EmsInterviewMode {
  ONLINE = 0,
  OFFLINE = 1,
}

export enum EmsLeaveType {
  EXAM,
  PERSONAL,
  SICK,
  UNPAID,
  OTHERS,
}

export enum EmsCalendarEventType {
  HOLIDAY,
  WEEKOFF,
  EVENT,
}

export enum EmsOrganizationSettingType {
  SICK_LEAVE,
  PERSONAL_LEAVE,
  EXAM_LEAVE,
  OTHER_LEAVE,
  UNPAID_LEAVE,
  WORKING_HOUR,
  DESCRIPTION,
  BRAND_NAME,
  LOGO,
  PHONE,
  ADDRESS,
  TOTAL_EMPLOYEE,
  HOLIDAY,
  WEEKOFF,
  SIGN_IN,
  SIGN_OUT,
  SALARY_CIRCLE_START_DATE,
  SALARY_CIRCLE_END_DATE,
}

export enum EmsOrganizationSettingLabels {
  Logo = 'Logo',
  TotalAllowEmployee = 'Total Allow Employee',
  SalaryDay = 'Salary Day',
  Phone = 'Phone No.',
  OfficialEmail = 'Official Email',
  Website = 'Website',
  Address = 'Address',
  Description = 'Description',
  IncentiveDay = 'Incentive Day',
}

export enum EmsRoleSettingLabels {
  LateLogin = 'Late Login (min)',
  EarlyLogout = 'Early Logout (min)',
  PunchIn = 'Punch In Time',
  PunchOut = 'Punch Out Time',
  WeekOff = 'Week Off',
  Holidays = 'Calendar',
  isAdmin = 'Admin?',
}

export enum EmsRoleRequestLabels {
  SickLeave = 'Sick Leave',
  CasualLeave = 'Casual Leave',
  AnnualLeave = 'Annual Leave',
  MaternityLeave = 'Maternity Leave',
  PaternityLeave = 'Paternity Leave',
  BereavementLeave = 'Bereavement Leave',
  WorkFromHome = 'Work From Home',
}

export enum EmsSalaryStructureCategory {
  NONPF = 0,
  PF1 = 1,
  PF2 = 2,
}
export enum EmsUpdatedSalaryStructureCategory {
  NONPF = 0,
  PF = 1,
  ESI = 2,
}
export enum EmsGovtSub {
  ABRY = 0,
  NotApplicable = 1,
}

export enum EmsSkillCategory {
  Unskilled = 0,
  SemiSkilled = 1,
  Skilled = 2,
  HighlySkilled = 3,
  NotFollowed = 4,
}
export enum EmsAnnouncementType {
  Announcement = 0,
  Birthday = 1,
  WorkAnniversary = 2,
  CalendarEvent = 3,
}

export enum EmsIncentiveType {
  Monthly = 'Monthly',
  Quarterly = 'Quarterly',
  Yearly = 'Yearly',
  HalfYearly = 'Half yearly',
}

export enum EmsDeductType {
  Monthly = 'Monthly',
  Quarterly = 'Quarterly',
  Yearly = 'Yearly',
  HalfYearly = 'Half yearly',
}
export enum EmsAdvanceStatus {
  Request = 'Request',
  Approved = 'Approved',
  Pending = 'Pending',
  HrApproved = 'Approved By Hr',
  Rejected = 'Rejected',
  Canceled = 'Canceled',
}
