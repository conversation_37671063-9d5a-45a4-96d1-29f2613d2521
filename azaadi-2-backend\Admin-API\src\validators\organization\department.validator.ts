import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain, validationResult } from "express-validator";
import { isValidObjectId } from "mongoose";

/**
 * Class to handle department and designation-related request validations
 */
export class DepartmentValidator {
    /**
     * Get validation rules for department creation/update
     */
    public static getDepartmentValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Department name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Department name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Department name must be between 2 and 100 characters"),

            // Organization ID validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Department Head validation (optional)
            body("tDepartmentHead")
                .optional()
                .custom((value) => {
                    if (!isValidObjectId(value)) {
                        throw new Error("Invalid department head role ID format");
                    }
                    return true;
                }),

            // Department tag validation (optional)
            body("sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Department tag can only contain letters, numbers, hyphens and underscores")
                .isLength({ min: 2, max: 50 })
                .withMessage("Department tag must be between 2 and 50 characters"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for designation creation/update
     */
    public static getDesignationValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Designation name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Designation name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Designation name must be between 2 and 100 characters"),

            // Organization ID validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Designation tag validation (optional)
            body("sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Designation tag can only contain letters, numbers, hyphens and underscores")
                .isLength({ min: 2, max: 50 })
                .withMessage("Designation tag must be between 2 and 50 characters"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
