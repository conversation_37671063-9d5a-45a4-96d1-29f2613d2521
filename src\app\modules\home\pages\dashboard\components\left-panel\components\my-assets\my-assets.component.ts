import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { CardModule } from 'primeng/card';

import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { IAsset } from '../../../../models/Assets';
import { TranslatePipe } from '@ngx-translate/core';
@Component({
  selector: 'app-my-assets',
  imports: [
    CardModule,
    CommonModule,
    ButtonModule,
    TableModule,
    TagModule,
    TranslatePipe,
  ],
  templateUrl: './my-assets.component.html',
})
export class MyAssetsComponent {
  assets: IAsset[] = [
    {
      id: 1,
      name: 'Asset 1',
      imageUrl: 'assets/images/monitor.jpg',
      dateAcquired: new Date('2023-01-01'),
      status: 'Active',
    },
    {
      id: 2,
      name: 'Asset 2',
      imageUrl: 'assets/images/computer-mouse.webp',
      dateAcquired: new Date('2023-02-01'),
      status: 'Inactive',
    },
    {
      id: 3,
      name: 'Asset 3',
      imageUrl: 'assets/images/headphone.webp',
      dateAcquired: new Date('2023-03-01'),
      status: 'Active',
    },
    {
      id: 4,
      name: 'Asset 4',
      imageUrl: 'assets/images/keyboard.jpeg',
      dateAcquired: new Date('2023-04-01'),
      status: 'Inactive',
    },
    {
      id: 5,
      name: 'Asset 5',
      imageUrl: 'assets/images/monitor.jpg',
      dateAcquired: new Date('2023-05-01'),
      status: 'Active',
    },
  ];
}
