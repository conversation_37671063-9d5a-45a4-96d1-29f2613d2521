import { IManageAccess } from "../../interfaces/organization/manageAccess.interface";
import { ManageAccess } from "../../models";
import Repository from "../repository";
import IManageAccessRepository from "./abstracts/manageAccessRepository.abstract";

export default class ManageAccessRepository extends Repository<IManageAccess> implements IManageAccessRepository {
    constructor() {
        super(ManageAccess.model);
    }
}