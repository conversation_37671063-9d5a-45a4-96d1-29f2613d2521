import { model, Model, models, Schema } from "mongoose";
import { IBranch } from "../../interfaces/organization/organization.interface";

/**
 * Branch model class that defines the schema and provides access to the Branch collection.
 * 
 * @class Branch
 * @description Handles the schema definition and model creation for Branches
 */
export default class Branch {
    /**
     * Mongoose schema definition for Branch
     * @private
     */
    private static _schema = new Schema<IBranch>(
        {
            sName: {
                type: String,
                required: [true, 'Name is missing!'],
                trim: true,
                index: true // Indexed for faster search and filtering by branch name
            },
            sTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by branch tag
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking since we're not using optimistic concurrency control
        }
    );

    /**
     * Mongoose model for Branch
     * @private
     */
    private static _model: Model<IBranch> = models?.Branch || model<IBranch>("branch", Branch._schema);

    /**
     * Get the Mongoose model for Branch
     * @returns {Model<IBranch>} The Branch model
     */
    public static get model(): Model<IBranch> {
        return Branch._model;
    }
}