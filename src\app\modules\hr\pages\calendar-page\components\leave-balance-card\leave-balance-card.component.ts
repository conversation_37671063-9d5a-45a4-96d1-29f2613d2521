import { Component, input, OnInit } from '@angular/core';
import { CircularProgressComponent } from '@shared/components/circular-progress/circular-progress.component';
import { LeaveBalanceCardInput } from '../../models/leave_balance_card_input';

@Component({
  selector: 'app-leave-balance-card',
  imports: [CircularProgressComponent],
  templateUrl: './leave-balance-card.component.html',
})
export class LeaveBalanceCardComponent implements OnInit {
  data = input<LeaveBalanceCardInput>();
  progress = 0;
  color = '';
  ngOnInit(): void {
    const daysAvailable = this.data()?.daysAvailable;
    const totalDays = this.data()?.totalDays;
    this.progress = (100 * (daysAvailable ?? 0)) / (totalDays ?? 1);
    console.log(this.data()?.color);
    this.color = this.data()?.color ?? 'text-cyan-600';
  }
}
