{"common": {"hour": "Hr", "minute": "min"}, "Bassetti": "<PERSON><PERSON>", "Calendar": "Calendar", "Help & IT Support": "Help & IT Support", "Inceptial": "Inceptial", "Logout": "Logout", "My Profile": "My Profile", "Organization Setting": "Organization Setting", "header": {"loggedinUserName": "<PERSON><PERSON><PERSON>", "loggedinUserRole": "Manager"}, "Home": "Home", "Dashboard": "Dashboard", "Employee": "Employee", "Attendance": "Attendance", "Request": "Request", "Administrator": "Administrator", "Role": "Role", "Organization": "Organization", "Branches": "Branches", "Designation": "Designation", "HR_Policy": "HR Policy", "Privacy_Policy": "Privacy Policy", "IT_Support": "IT Support", "Email_Template": "<PERSON>ail Te<PERSON>late", "Shifts": "Shifts", "HR": "HR", "Announcements": "Announcements", "Calendars": "Calendars", "Manage_Documents": "Manage Documents", "Accounts": "Accounts", "Payslip": "Payslip", "Salary_Structure": "Salary Structure", "Salary_Difference_Report": "Salary Difference Report", "Other_Payment": "Other Payment", "Incentive": "Incentive", "Generate_Incentive": "Generate Incentive", "Advance_Payment": "Advance Payment", "QCM/Quiz": "QCM/Quiz", "Category": "Category", "Question": "Question", "Test": "Test", "Exam_Setting": "<PERSON><PERSON>", "Exam": "Exam", "RCM": "RCM", "Candidate": "Candidate", "Process": "Process", "Recruitment": "Recruitment", "Today": "Today", "dashboard": {"timeTracker": {"title": "Time Tracker", "remainingTime": "Remaining Time", "totalHours": "Total Hours", "shift": "Shift", "totalTimeWorked": "Total Time Worked"}, "myasset": {"myAssets": "My Assets", "name": "Name", "assignDate": "Assign Date", "action": "Action"}, "leaveBalance": {"title": "Leave Balance", "requestLeave": "Request Leave", "total": "Total", "remaining": "Remaining"}, "request": {"title": "Requests", "dateRange": "Date Range", "type": "Request Type", "category": "Category", "lastFourRecords": "Last 4 records"}, "teamAvailability": {"title": "Team Availability", "workingFromOffice": "Working from office", "workingFromHome": "Working from home", "onLeave": "On Leave"}, "attendanceReport": {"title": "Attendance Report", "today": "Today"}, "event": {"birthday": "Birthday", "birthdayWish": "Wish you all the success & happiness throughout the year", "anniversary": "Anniversary", "newJoinee": "New Joinee", "upcomingBirthday": "Upcoming Birthday", "upcomingAnniversary": "Upcoming Anniversary"}, "holiday": {"title": "Upcoming Holidays", "viewAll": "View All"}, "post": {"title": "Posts", "createPost": "Create Post", "noPost": "No Post Available", "noPostDescription": "Post the content here to share across all the employees"}}}