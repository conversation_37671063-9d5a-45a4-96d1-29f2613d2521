import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable()
export class TeamAvailabilityApiService {
  private readonly _http = inject(HttpClient);
  private readonly _baseUrl = environment.apiUrl;
  private readonly _requestUrl = `${this._baseUrl}/teamavailability`;

  fetchTeamAvailabilityData(): Observable<any> {
    return this._http.get<any>(`${this._requestUrl}/GetRequestData`);
  }
}
