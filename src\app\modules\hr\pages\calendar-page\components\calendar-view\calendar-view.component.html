<!-- week-calendar.component.html -->
<div class="border rounded-xl overflow-hidden bg-white h-full shadow-md">
  <div class="relative flex w-full">
    <button
      class="p-2 rounded-full absolute left-0 top-1/2 transform -translate-y-1/2 hover:bg-gray-200 focus:outline-none"
    >
      <i class="pi pi-chevron-left"></i>
    </button>
    <div
      class="grid grid-cols-7 text-center text-xs font-semibold border-b flex-1"
    >
      <div *ngFor="let day of weekDays" class="pt-2 text-gray-500 border-r">
        {{ day }}
      </div>
      <div
        *ngFor="let day of weekData"
        class="pb-2 text-gray-500 flex justify-center border-r"
      >
        <div
          class="text-black text-lg rounded-full w-8 h-8 flex justify-center items-center"
          [ngClass]="{ 'bg-green-700 text-white font-bold': day.isToday }"
        >
          {{ day.date }}
        </div>
      </div>
      <div class="border-r"></div>
      <div class="border-r"></div>
      <div class="border-r"></div>
      <div class="border-r"></div>
      <div class="border-r"></div>
      <div class="col-span-2 bg-[#239595] h-4 text-white">Week Off</div>
    </div>
    <button
      class="p-2 rounded-full absolute right-0 top-1/2 transform -translate-y-1/2 hover:bg-gray-200 focus:outline-none"
    >
      <i class="pi pi-chevron-right"></i>
    </button>
  </div>

  <div class="grid grid-cols-7 text-center h-full">
    <div
      *ngFor="let day of weekData"
      [ngClass]="{
        'bg-teal-100 text-white font-bold': day.isToday,
        'bg-red-100': day.isOutOfOffice,
        'bg-green-100': day.isLeave
      }"
      class="relative pt-2 text-sm border-r"
    >
      <div class="absolute w-full text-xs text-gray-600" *ngIf="day.label">
        <i class="pi pi-circle-fill text-xs mr-1 text-gray-500"></i>
        {{ day.label }}
      </div>
    </div>
  </div>
</div>
