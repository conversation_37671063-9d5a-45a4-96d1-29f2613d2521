<div [ngClass]="isSelected() ? 'bg-[var(--surface-ground)]' : ''" class="p-4 border border-[var(--border-color)] rounded-lg cursor-pointer  ">
    <div class="flex justify-between">
        <div class="flex gap-2">
            <img class="h-10 w-10 rounded-full object-cover" [src]="ticket().assignedTo.avatar" alt="">
            <p class="text-[18px] font-[500]">{{ticket().assignedTo.firstName}} {{ticket().assignedTo.lastName}} </p>
        </div>
        <p>{{ticket().createdAt | date: "dd.MM.YYYY"}} </p>
    </div>
    <p class="text-ellipsis whitespace-nowrap overflow-hidden" >{{ticket().description}}</p>
    <div class="flex gap-2 mt-4 text-[10px]">
        <p-tag [value]="ticket().priority | titlecase" [severity]="ticket().priority | ticketPriorityColor" rounded="true"/>
        <p-tag [value]="ticket().status | titlecase"  styleClass="text-[10px]" rounded="true" [severity]="ticket().status === 'OPEN' ? 'danger': 'success'" />
        <p-tag [value]="ticket().department | titlecase" [severity]="ticket().department | departmentColor" rounded="true" />
    </div>
</div>