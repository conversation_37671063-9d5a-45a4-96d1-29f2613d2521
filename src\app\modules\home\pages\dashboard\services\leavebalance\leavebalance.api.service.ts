import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ELeaveType, ILeaveBalance } from '../../models/leave';

@Injectable()
export class LeaveBalanceApiService {
  private readonly _http = inject(HttpClient);
  private readonly _baseUrl = environment.apiUrl;
  private readonly _leavebalanceUrl = `${this._baseUrl}/TimeTracker`;

  fetchRequestData(): Observable<ILeaveBalance[]> {
    // return this._http.get<ILeaveBalance>(
    //   `${this._leavebalanceUrl}/GetRequestData`
    // );
    const mockData: ILeaveBalance[] = [
      {
        aCount: 10,
        aRemaining: 5,
        leaveType: ELeaveType.AnnualLeave,
      },
      {
        aCount: 10,
        aRemaining: 5,
        leaveType: ELeaveType.SickLeave,
      },
      {
        aCount: 10,
        aRemaining: 5,
        leaveType: ELeaveType.CasualLeave,
      },
    ];

    return of(mockData);
  }
}
