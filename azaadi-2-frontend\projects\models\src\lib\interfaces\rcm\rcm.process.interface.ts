import {
    EmsFileType,
    EmsInterviewMode,
    EmsProcessType,
  } from '../../enums/managements.enum';
  import { ICommonName } from '../common.interface';
  import { EmsRole } from '../organization/manageOrganization.interface';
  import { EmsUser } from '../users/user.interface';
import { EmsRcmUser } from './rcm.user.interface';
  
  interface EmsRcmGeneral extends Partial<ICommonName>{
  }
  
  interface BStatus{
    bStatus?: boolean;
    sComments?: string;
  }
  
  interface TUser{
    tUser?: string | EmsRcmUser;
  }
  
  interface SUrl{
    sUrl?: string;
  }
  
  interface SDateTime{
    tInterviewer?: string | EmsRole;
    dStartDateTime?: Date;
    dEndDateTime?: Date;
  }
  
  interface SMarks{
    aMarks?: number;
    aObtainedMarks?: number;
  }
  
  interface TInterview{
    tReviewer?: string | EmsRole;
  }
  
  export interface EmsRecruitment extends EmsRcmGeneral,TUser {
    tRcmProcess?: string | EmsRcmProcess;
    tCreatedBy?: string | EmsUser;
    bComplete?: boolean;
  }
  
  export interface EmsRcmProcess extends EmsRcmGeneral {
    sSteps?: string[] | EmsRcmSteps[];
  }
  
  export interface EmsRcmSteps extends EmsRcmGeneral,Pick<BStatus,'bStatus'> {
    eProcessType?: EmsProcessType;
    tProcessData?:
    | string
    | EmsRcmExam
    | EmsRcmInterview
    | EmsRcmVerification
    | EmsRcmDocuments;
  }
  
  export interface EmsRcmInterview extends EmsRcmGeneral ,BStatus,TUser,SUrl,SDateTime,SMarks{
  }
  
  export interface EmsRcmVerification extends EmsRcmGeneral ,BStatus,TUser,TInterview{
  }
  
  export interface EmsRcmDocuments extends EmsRcmGeneral ,BStatus,TUser,TInterview{
    eType?: EmsFileType[];
    bAllowMultiple?: boolean;
    tDocumentDetails?: EmsRcmDocumentDetails[];
  }
  
  export interface EmsRcmDocumentDetails extends Pick<BStatus,'bStatus'>,EmsRcmGeneral ,SUrl{
    sSize?: string;
    eType?: EmsFileType;
  }
  
  export interface EmsRcmExam extends EmsRcmGeneral,BStatus,Omit<SDateTime,'tInterviewer'>,Pick<SMarks,'aMarks'>{
    aPassMark?: number;
  }
  
  export interface EmsRcmInterview extends EmsRcmGeneral,BStatus,TUser ,SUrl,SDateTime,SMarks{
    eInterviewMode?: EmsInterviewMode;
    sAddress?: string;
    sMapUrl?: string;
  }
  