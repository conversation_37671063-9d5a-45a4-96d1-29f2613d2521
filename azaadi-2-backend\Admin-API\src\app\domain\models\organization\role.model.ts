import { model, Model, models, Schema } from "mongoose";
import { IRole } from "../../interfaces/organization/role.interface";

/**
 * Role model class that defines the schema and provides access to the Role collection.
 * 
 * @class Role
 * @description Handles the schema definition and model creation for Roles
 */
export default class Role {
    /**
     * Mongoose schema definition for Role
     * @private
     */
    private static _schema = new Schema<IRole>(
        {
            sName: {
                type: String,
                trim: true
            },
            sTag: {
                type: String,
                trim: true,
                required: [true, "Role tag is missing"],
                index: true // Indexed for quick lookups by tag
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                index: true // Indexed for faster lookups and joins by organization
            },
            tHead: {
                type: Schema.Types.ObjectId,
                ref: "role",
                index: true // Indexed for hierarchical role queries and filtering
            },
            tHrReport: {
                type: Schema.Types.ObjectId,
                ref: "role",
                index: true // Indexed for HR reporting structure queries
            },
            tAdditionalOrg: [{
                type: Schema.Types.ObjectId,
                ref: "organization"
            }],
            bIsCreatedBySuperAdmin: {
                type: Boolean,
                default: false
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for Role
     * @private
     */
    private static _model: Model<IRole> = models?.Role || model<IRole>("role", Role._schema);

    /**
     * Get the Mongoose model for Role
     * @returns {Model<IRole>} The Role model
     */
    public static get model(): Model<IRole> {
        return Role._model;
    }
}