import { CommonModule } from '@angular/common';
import { Component, input } from '@angular/core';
import { IPost } from '@shared/models';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CreatePostComponent } from './components/create-post/create-post.component';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
  selector: 'app-post',
  imports: [
    ButtonModule,
    CommonModule,
    CardModule,
    CreatePostComponent,
    TranslatePipe,
  ],
  templateUrl: './post.component.html',
})
export class PostComponent {
  posts = input.required<IPost[]>();
}
