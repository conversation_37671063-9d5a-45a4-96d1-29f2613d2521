import { ISalaryHistory } from "../../interfaces/organization/salaryReport.interface";
import { SalaryHistory } from "../../models";
import Repository from "../repository";
import ISalaryHistoryRepository from "./abstracts/salaryHistoryRepository.abstract";

export default class SalaryHistoryRepository extends Repository<ISalaryHistory> implements ISalaryHistoryRepository {
    constructor() {
        super(SalaryHistory.model);
    }
}