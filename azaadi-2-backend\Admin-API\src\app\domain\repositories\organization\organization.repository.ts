import { IOrganization } from "../../interfaces/organization/organization.interface";
import { Organization } from "../../models";
import Repository from "../repository";
import IOrganizationRepository from "./abstracts/organizationRepository.abstract";

export default class OrganizationRepository extends Repository<IOrganization> implements IOrganizationRepository {
    constructor() {
        super(Organization.model);
    }
}