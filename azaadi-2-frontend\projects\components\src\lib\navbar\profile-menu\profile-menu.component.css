.menu-image{
    width: 60px;
    height: 60px;
    object-fit: cover;
    object-position: center;
    border-radius: 50%;
}

.div-grid{
    display: inline-flex;
}
mat-list-item{
    min-width: 280px !important;
    div{
        cursor: pointer;
    }
    
}
.mat-mdc-slide-toggle{
    transform: scale(0.85);
    float: right;
    margin-top: 15px;
}
.mdc-list-item--with-leading-icon.mdc-list-item{
    padding-right:0;
}
.link {
    color: #4285ff;
    cursor: pointer;
}