import { Types } from 'mongoose';
import { IPartialOrganization, IOrganization } from './organization.interface';
import { IConcept, IConceptBase } from '../concept.interface';

export interface IRole extends IConcept {
    sName: string;
    sTag: string;
    tOrganization: string | Types.ObjectId | IOrganization;
    tHead?: string | Types.ObjectId | IRole;
    tHrReport?: string | Types.ObjectId | IRole;
    tAdditionalOrg?: string[] | Types.ObjectId[] | IOrganization[];
    bIsCreatedBySuperAdmin: boolean;
}

export interface IRoleSettings extends IConcept {
    tRole: string | Types.ObjectId | IRole;
    sTitle: string;
    sType?: string;
    sValue?: any;
    tManagedBy?: string | Types.ObjectId | IRole;
}

export interface IPartialRole extends IConceptBase {
    tOrganization?: IPartialOrganization | string | Types.ObjectId;
}