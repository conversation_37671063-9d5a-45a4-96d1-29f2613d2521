import { IPrivacyPolicy } from "../../interfaces/organization/policies.interface";
import { PrivacyPolicy } from "../../models";
import Repository from "../repository";
import IPrivacyPolicyRepository from "./abstracts/privacyPolicyRepository.abstract";

export default class PrivacyPolicyRepository extends Repository<IPrivacyPolicy> implements IPrivacyPolicyRepository {
    constructor() {
        super(PrivacyPolicy.model);
    }
}