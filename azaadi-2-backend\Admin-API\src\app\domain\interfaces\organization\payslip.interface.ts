import { Types } from 'mongoose';
import { EMSSalaryStructureCategory, EMSUpdatedSalaryStructureCategory } from '../../../../utils/meta/enum.utils';
import { IConcept } from '../concept.interface';
import { ILeaveBalance } from './manageRequest.interface';
import { ISalaryStructureTemplate } from './salaryStructure.interface';
import { IPertialUser } from '../user/user.interface';
import { IUserBankInfo } from '../user/user-document.interface';

export interface IPayslip extends IConcept {
    tIdUser: string | Types.ObjectId;
    aMonth: number;
    aYear: number;
    aSalaryDay?: number;
    dStartDate?: Date;
    dEndDate?: Date;
    tSalaryStructure?: Record<string, any>;
    eType?: EMSSalaryStructureCategory;
    bIsReportingAuthApproved: boolean;
    bIsHrApproved: boolean;
    aMiscAmount: number;
    aMiscDescription?: string;
    user?: IPertialUser;
    userBank?: IUserBankInfo;
    leaveBalance?: ILeaveBalance[];
    salaryStructureTemplate?: ISalaryStructureTemplate;
}

export interface IUpdatedPayslip extends Omit<IPayslip, 'eType'> {
    eType?: EMSUpdatedSalaryStructureCategory;
}