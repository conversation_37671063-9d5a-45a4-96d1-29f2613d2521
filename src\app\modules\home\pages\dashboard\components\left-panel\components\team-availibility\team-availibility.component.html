<p-card>
  <h1 class="text-[20px] font-semibold">
    {{ "dashboard.teamAvailability.title" | translate }}
  </h1>
  <app-users-tray
    bgColor="bg-green-500"
    [users]="workingRemotely"
    [colCount]="5"
  />
  <app-users-tray
    bgColor="bg-purple-600"
    icon="pi pi-home "
    [users]="workingRemotely"
    [colCount]="5"
  />
  <app-users-tray
    bgColor="bg-red-500"
    [users]="workingRemotely"
    [colCount]="5"
  />
  <div class="flex w-full justify-center gap-8 mt-6">
    <div class="inline-flex items-center gap-1">
      <app-badge bgColor="bg-green-500" />
      <span>
        {{ "dashboard.teamAvailability.workingFromOffice" | translate }}
      </span>
    </div>
    <div class="inline-flex items-center gap-1">
      <app-badge bgColor="bg-purple-600" icon="pi pi-home" />
      <span>
        {{ "dashboard.teamAvailability.workingFromHome" | translate }}
      </span>
    </div>
    <div class="inline-flex items-center gap-1">
      <app-badge bgColor="bg-red-500" />
      <span> {{ "dashboard.teamAvailability.onLeave" | translate }} </span>
    </div>
  </div>
</p-card>
