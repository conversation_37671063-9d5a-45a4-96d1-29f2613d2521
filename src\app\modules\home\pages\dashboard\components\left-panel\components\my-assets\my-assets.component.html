<p-card>
  <h1 class="text-[17px] font-semibold mb-4">My Assets</h1>

  <p-table [value]="assets" styleClass="text-[10px] " [size]="'small'">
    <ng-template #header>
      <tr>
        <th class="w-1/3" scope="col">
          {{ "dashboard.myasset.name" | translate }}
        </th>
        <th class="w-1/3" scope="col">
          <p class="text-center">
            {{ "dashboard.myasset.assignDate" | translate }}
          </p>
        </th>
        <th class="w-1/3" scope="col">
          <p class="text-center">
            {{ "dashboard.myasset.action" | translate }}
          </p>
        </th>
      </tr>
    </ng-template>
    <ng-template #body let-asset>
      <tr>
        <td class="w-1/3">
          <p
            class="bg-[var(--p-primary-200)] text-center text-[var(--p-primary-900)] rounded-3xl px-1.5"
          >
            {{ asset.name }}
          </p>
        </td>
        <td class="w-1/3">
          <p class="text-center">
            {{ asset.dateAcquired | date : "MM.dd.yyyy" }}
          </p>
        </td>
        <td class="w-1/3">
          <div class="flex justify-center">
            <span
              class="pi pi-ellipsis-v scale-75 text-[var(--p-primary-400)] hover:text-[var(--p-primary-600)] cursor-pointer"
            ></span>
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</p-card>
