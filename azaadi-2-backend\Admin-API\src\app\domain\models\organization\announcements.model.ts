import { model, Model, models, Schema } from "mongoose";
import { IAnnouncement } from "../../interfaces/organization/calendar.interface";

/**
 * Announcement model class that defines the schema and provides access to the Announcement collection.
 * 
 * @class Announcement
 * @description Handles the schema definition and model creation for Announcements
 */
export default class Announcement {
    /**
     * Mongoose schema definition for Announcement
     * @private
     */
    private static _schema = new Schema<IAnnouncement>(
        {
            sName: {
                type: String,
                required: [true, 'Name is missing!'],
                trim: true,
                index: true // Indexed for faster search and filtering by announcement name
            },
            sDescription: {
                type: String,
                trim: true
            },
            sUrl: {
                type: String,
                trim: true
            },
            dStartDate: {
                type: Date,
                index: true // Indexed for date-based queries and filtering
            },
            dEndDate: {
                type: Date,
                index: true // Indexed for date-based queries and filtering
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                required: [true, 'Organization is missing!'],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for Announcement
     * @private
     */
    private static _model: Model<IAnnouncement> = models?.Announcement || model<IAnnouncement>("announcement", Announcement._schema);

    /**
     * Get the Mongoose model for Announcement
     * @returns {Model<IAnnouncement>} The Announcement model
     */
    public static get model(): Model<IAnnouncement> {
        return Announcement._model;
    }
}