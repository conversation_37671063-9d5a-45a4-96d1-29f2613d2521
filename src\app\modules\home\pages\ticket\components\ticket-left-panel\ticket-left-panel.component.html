<div
  class="w-[450px] p-4 h-[calc(100vh-9rem)] overflow-auto bg-[var(--p-card-background)] rounded-lg"
>
  <div class="flex items-center justify-between gap-6">
    <p-inputgroup>
      <input pInputText [(ngModel)]="searchTicket" placeholder="Search ticket" />
  
      <p-inputgroup-addon>
        <i class="pi pi-search"></i>
      </p-inputgroup-addon>
    </p-inputgroup>
    <img src="assets/images/filter.svg" class="h-5 w-5 object-cover cursor-pointer" alt="">
  </div>

  <p class="my-4 text-[20px] font-[500]">
    My Open Tickets {{ "(" + tickets().length + ")" }}
  </p>

  <div
    (click)="navigateToTicket(ticket.id)"
    class="mb-2 rounded hover:bg-[var(--hover)] "
    *ngFor="let ticket of tickets()"
  >
    <app-ticket-card [isSelected]="ticket.id === selectedTicket()"  [ticket]="ticket"></app-ticket-card>
  </div>
</div>
