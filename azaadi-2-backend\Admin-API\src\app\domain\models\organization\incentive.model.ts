import { model, Model, models, Schema } from "mongoose";
import { IIncentive, IPeriodicDetails } from "../../interfaces/organization/incentive.interface";
import { EMSIncentiveType } from "../../../../utils/meta/enum.utils";

/**
 * Incentive model class that defines the schema and provides access to the Incentive collection.
 * 
 * @class Incentive
 * @description Handles the schema definition and model creation for Incentives
 */
export default class Incentive {
    /**
     * Mongoose schema definition for periodic details
     * @private
     */
    private static _periodicDetailsSchema = new Schema<IPeriodicDetails>(
        {
            aMonth: {
                type: Number,
                required: [true, 'Month is required'],
                index: true // Indexed for filtering by month
            },
            aYear: {
                type: Number,
                required: [true, 'Year is required'],
                index: true // Indexed for filtering by year
            },
            aAmount: {
                type: Number,
                required: [true, 'Amount is required']
            }
        },
        {
            timestamps: true,
            _id: true // Maintain separate IDs for periodic details
        }
    );

    /**
     * Mongoose schema definition for Incentive
     * @private
     */
    private static _schema = new Schema<IIncentive>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                required: [true, 'User ID is required'],
                index: true // Indexed for faster lookups by user
            },
            dStartDate: {
                type: Date,
                required: [true, 'Start date is required'],
                index: true // Indexed for date-based queries
            },
            dEndDate: {
                type: Date,
                required: [true, 'End date is required'],
                index: true // Indexed for date-based queries
            },
            aTotalAmount: {
                type: Number,
                required: [true, 'Total amount is required']
            },
            aPeriodAmount: {
                type: Number,
                required: [true, 'Period amount is required']
            },
            tPeriodicDetails: {
                type: [Incentive._periodicDetailsSchema],
                default: []
            },
            eType: {
                type: String,
                enum: EMSIncentiveType,
                required: [true, 'Incentive type is required'],
                index: true // Indexed for filtering by type
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                required: [true, 'Organization is required'],
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for Incentive
     * @private
     */
    private static _model: Model<IIncentive> = models?.Incentive || model<IIncentive>("incentive", Incentive._schema);

    /**
     * Get the Mongoose model for Incentive
     * @returns {Model<IIncentive>} The Incentive model
     */
    public static get model(): Model<IIncentive> {
        return Incentive._model;
    }
}