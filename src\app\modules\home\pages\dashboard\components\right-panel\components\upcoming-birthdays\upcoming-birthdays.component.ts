import { Component, signal } from '@angular/core';
import { IBirthday } from '../../../../models/birthday';
import { CommonModule } from '@angular/common';
import { TagModule } from 'primeng/tag';
import { RelativeDatePipe } from '@shared/pipes/relative-date.pipe';
import { ButtonModule } from 'primeng/button';
import { DividerModule } from 'primeng/divider';
import { CarouselModule } from 'primeng/carousel';
import { TranslatePipe } from '@ngx-translate/core';
@Component({
  selector: 'app-upcoming-birthdays',
  imports: [
    CommonModule,
    TagModule,
    RelativeDatePipe,
    ButtonModule,
    DividerModule,
    CarouselModule,
    TranslatePipe,
  ],
  templateUrl: './upcoming-birthdays.component.html',
  styleUrl: './upcoming-birthdays.component.scss',
})
export class UpcomingBirthdaysComponent {
  upcomingBirthdays = signal<IBirthday[]>([
    {
      user: {
        firstName: '<PERSON><PERSON>',
        lastName: '<PERSON>',
        image: 'https://randomuser.me/api/portraits/men/1.jpg',
      },
      birthdayDate: new Date(new Date().setDate(new Date().getDate() + 1)),
    },
    {
      user: {
        firstName: 'Anjali',
        lastName: 'Verma',
        image: 'https://randomuser.me/api/portraits/women/2.jpg',
      },
      birthdayDate: new Date(new Date().setDate(new Date().getDate() + 2)),
    },
    {
      user: {
        firstName: 'Ravi',
        lastName: 'Kumar',
        image: 'https://randomuser.me/api/portraits/men/3.jpg',
      },
      birthdayDate: new Date(new Date().setDate(new Date().getDate() + 3)),
    },
    {
      user: {
        firstName: 'Sneha',
        lastName: 'Patel',
        image: 'https://randomuser.me/api/portraits/women/4.jpg',
      },
      birthdayDate: new Date(new Date().setDate(new Date().getDate() + 4)),
    },
    {
      user: {
        firstName: 'Amit',
        lastName: 'Gupta',
        image: 'https://randomuser.me/api/portraits/men/5.jpg',
      },
      birthdayDate: new Date(new Date().setDate(new Date().getDate() + 5)),
    },
    {
      user: {
        firstName: 'Priya',
        lastName: 'Singh',
        image: 'https://randomuser.me/api/portraits/women/6.jpg',
      },
      birthdayDate: new Date(new Date().setDate(new Date().getDate() + 6)),
    },
  ]);
}
