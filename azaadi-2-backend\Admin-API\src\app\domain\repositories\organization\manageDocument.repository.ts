import { IManageDocument } from "../../interfaces/organization/manageDocument.interface";
import { ManageDocument } from "../../models";
import Repository from "../repository";
import IManageDocumentRepository from "./abstracts/manageDocumentRepository.abstract";

export default class ManageDocumentRepository extends Repository<IManageDocument> implements IManageDocumentRepository {
    constructor() {
        super(ManageDocument.model);
    }
}