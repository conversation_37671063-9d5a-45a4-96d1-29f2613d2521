import { IIncentiveDetails } from "../../interfaces/organization/incentive.interface";
import { IncentiveDetail } from "../../models";
import Repository from "../repository";
import IIncentiveDetailRepository from "./abstracts/incentiveDetailRepository.abstract";

export default class IncentiveDetailRepository extends Repository<IIncentiveDetails> implements IIncentiveDetailRepository {
    constructor() {
        super(IncentiveDetail.model);
    }
}