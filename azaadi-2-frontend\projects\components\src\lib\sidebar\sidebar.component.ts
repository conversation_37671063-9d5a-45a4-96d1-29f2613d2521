import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatSidenavModule } from '@angular/material/sidenav';
import { NavItem } from '../../../../models/src/public-api';

@Component({
  selector: 'lib-sidebar',
  standalone: true,
  imports: [CommonModule,MatSidenavModule, MatListModule, MatExpansionModule,MatIconModule,],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css',
})
export class SidebarComponent {
  @Input() menu: NavItem[] = [];
  @Input()activeUrl:string='';
  mobileQuery?: MediaQueryList;
  @Output() clickMenuOption = new EventEmitter<string>();
 

  constructor(){
  }

  
  onMenuOptionClick(route:string) {
    this.clickMenuOption.emit(route);
  }

  isChildOpen(items:NavItem[]):boolean{
    return items.some(e=>e.route==this.activeUrl);
  }
}