import { Component, input } from '@angular/core';
import { CardModule } from 'primeng/card';
import { InfoCardInput } from '../../models/info-card-input';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-info-card',
  imports: [CardModule,CommonModule],
  templateUrl: './info-card.component.html',
  styleUrl: './info-card.component.scss'
})
export class InfoCardComponent {
  data = input<InfoCardInput|null>(null);
}
