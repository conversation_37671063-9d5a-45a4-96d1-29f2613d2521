import { IColumn } from '../../models/Column';

export const filteredResult = (
  keys: IColumn[],
  data: any[],
  filter: string,
  searchText: string
) => {
  const normalizedSearchText = searchText.toLowerCase().trim();
  return data.filter((item) => {
    if (filter === 'All') {
      return keys.some((key) => {
        const columnValue = item[key.field as keyof typeof item];
        return columnValue
          ?.toString()
          .toLowerCase()
          .includes(normalizedSearchText);
      });
    } else {
      const columnValue = item[filter as keyof typeof item];
      return columnValue
        ?.toString()
        .toLowerCase()
        .includes(normalizedSearchText);
    }
  });
};
