import { ICalendarTemplate } from "../../interfaces/organization/calendar.interface";
import { Calendar } from "../../models";
import Repository from "../repository";
import ICalendarRepository from "./abstracts/calendarRepository.abstract";

export default class CalendarRepository extends Repository<ICalendarTemplate> implements ICalendarRepository{
    constructor(){
        super(Calendar.model);
    }
}