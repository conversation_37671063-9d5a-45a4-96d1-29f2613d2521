{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387

    "version": "0.2.0",
    "configurations": [
        {
            "type": "node-terminal",
            "request": "launch",
            "name": "Admin-API (npm start:dev)",
            "command": "npm run start:dev",
            "cwd": "${workspaceFolder}/azaadi-2-backend/Admin-API"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Admin-API (ts-node)",
            "program": "${workspaceFolder}/azaadi-2-backend/Admin-API/src/index.ts",
            "runtimeExecutable": "${workspaceFolder}/azaadi-2-backend/Admin-API/node_modules/.bin/ts-node",
            "cwd": "${workspaceFolder}/azaadi-2-backend/Admin-API",
            "sourceMaps": true,
            "env": {
                "NODE_ENV": "development"
            },
            "console": "integratedTerminal",
            "autoAttachChildProcesses": true,
            "skipFiles": ["<node_internals>/**"]
        }
    ]
}
