import { IOrganizationSetting } from "../../interfaces/organization/organization.interface";
import { OrganizationSettings } from "../../models";
import Repository from "../repository";
import IOrganizationSettingsRepository from "./abstracts/organizationSettingsRepository.abstract";

export default class OrganizationSettingsRepository extends Repository<IOrganizationSetting> implements IOrganizationSettingsRepository {
    constructor() {
        super(OrganizationSettings.model);
    }
}