import { Schema } from "mongoose";
import { IUserBankInfo } from "../../interfaces/user/user-document.interface";
import { ILeaveBalance } from "../../interfaces/organization/manageRequest.interface";
import { IPartialEmployeeCode } from "../../interfaces/user/employee-code.interface";
import { IConceptBase } from "../../interfaces/concept.interface";
import { IPartialRole } from "../../interfaces/organization/role.interface";
import { IPartialOrganization } from "../../interfaces/organization/organization.interface";

/**
 * Shared schema definitions for payslip-related models
 */
export class PayslipSchemas {
    /**
     * Common schema for user bank information
     */
    static readonly userBankSchema = new Schema<IUserBankInfo>({
        sId: { type: String },
        sBankName: { type: String },
        sBankAccount: { type: String },
        sIfscCode: { type: String },
        sBranchName: { type: String }
    }, { _id: false });

    /**
     * Common schema for leave balance
     */
    static readonly leaveBalanceSchema = new Schema<ILeaveBalance>({
        aCount: { type: Number },
        sType: { type: String },
        sRoleId: { type: String }
    }, { _id: false });

    /**
     * Common schema for base concept properties
     */
    static readonly commonSchema = new Schema<IConceptBase>({
        sId: { type: String },
        sName: { type: String }
    }, { _id: false });

    /**
     * Common schema for employee code
     */
    static readonly employeeCodeSchema = new Schema<IPartialEmployeeCode>({
        sId: { type: String },
        aPunchId: { type: Number },
        sCode: { type: String }
    }, { _id: false });

    /**
     * Common schema for organization
     */
    static readonly organizationSchema = new Schema<IPartialOrganization>({
        sId: { type: String },
        sName: { type: String },
        sTag: { type: String }
    }, { _id: false });

    /**
     * Common schema for role
     */
    static readonly roleSchema = new Schema<IPartialRole>({
        sId: { type: String },
        sName: { type: String },
        tOrganization: { type: PayslipSchemas.organizationSchema }
    }, { _id: false });

    /**
     * Common schema for user
     */
    static readonly userSchema = new Schema({
        sId: { type: String },
        sName: { type: String },
        sEmail: { type: String },
        aPhoneNumber: { type: String },
        sProfileUrl: { type: String },
        dInactiveDate: { type: Date },
        dJoinDate: { type: Date },
        aAadharNo: { type: String },
        tDepartment: { type: PayslipSchemas.commonSchema },
        tDesignation: { type: PayslipSchemas.commonSchema },
        tIdEmployee: { type: PayslipSchemas.employeeCodeSchema },
        tRole: { type: PayslipSchemas.roleSchema }
    }, { _id: false });

    /**
     * Common payslip base schema configuration
     */
    static readonly basePayslipConfig = {
        timestamps: true,
        versionKey: false // Disable the __v field in documents
    };
}