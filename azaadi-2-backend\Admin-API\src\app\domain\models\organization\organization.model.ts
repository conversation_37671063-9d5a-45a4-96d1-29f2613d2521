import { model, Model, models, Schema } from "mongoose";
import { IOrganization } from "../../interfaces/organization/organization.interface";

/**
 * Organization model class that defines the schema and provides access to the Organization collection.
 * 
 * @class Organization
 * @description Handles the schema definition and model creation for Organizations
 */
export default class Organization {
    /**
     * Mongoose schema definition for Organization
     * @private
     */
    private static _schema = new Schema<IOrganization>(
        {
            sName: {
                type: String,
                required: [true, 'Organization name is required'],
                trim: true,
                index: true // Indexed for faster search and filtering by organization name
            },
            sTag: {
                type: String,
                required: [true, 'Organization tag is required'],
                trim: true,
                unique: true,
                index: true // Indexed for unique constraint and faster lookup by tag
            },
            bIsCreatedBySuperAdmin: {
                type: Boolean,
                default: false
            },
            bIsActive: {
                type: Boolean,
                default: true,
                index: true // Indexed to optimize filtering of active/inactive organizations
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking since we're not using optimistic concurrency control
        }
    );

    /**
     * Mongoose model for Organization
     * @private
     */
    private static _model: Model<IOrganization> = models?.Organization || model<IOrganization>("organization", Organization._schema);

    /**
     * Get the Mongoose model for Organization
     * @returns {Model<IOrganization>} The Organization model
     */
    public static get model(): Model<IOrganization> {
        return Organization._model;
    }
}