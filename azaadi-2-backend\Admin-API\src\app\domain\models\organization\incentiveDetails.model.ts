import { model, Model, models, Schema } from "mongoose";
import { IIncentiveDetails } from "../../interfaces/organization/incentive.interface";
import { EMSIncentiveType } from "../../../../utils/meta/enum.utils";

/**
 * IncentiveDetails model class that defines the schema and provides access to the IncentiveDetails collection.
 * 
 * @class IncentiveDetails
 * @description Handles the schema definition and model creation for Incentive Details
 */
export default class IncentiveDetails {
    /**
     * Mongoose schema definition for IncentiveDetails
     * @private
     */
    private static _schema = new Schema<IIncentiveDetails>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                ref: "login",
                required: [true, "User ID is required"],
                index: true // Indexed for faster lookups by user
            },
            eType: {
                type: String,
                enum: EMSIncentiveType,
                required: [true, "Incentive type is required"],
                index: true // Indexed for filtering by type
            },
            aTotalAmount: {
                type: Number,
                required: [true, "Total amount is required"]
            },
            dStartDate: {
                type: Date,
                required: [true, "Start date is required"],
                index: true // Indexed for date-based queries
            },
            dEndDate: {
                type: String,
                required: [true, "End date is required"]
            },
            dDate: {
                type: Date,
                required: [true, "Date is required"],
                trim: true,
                index: true // Indexed for date-based queries
            },
            aAmount: {
                type: Number,
                required: [true, "Amount is required"]
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: "organization",
                required: [true, "Organization is required"],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for IncentiveDetails
     * @private
     */
    private static _model: Model<IIncentiveDetails> = models?.IncentiveDetails || model<IIncentiveDetails>("incentivedetail", IncentiveDetails._schema);

    /**
     * Get the Mongoose model for IncentiveDetails
     * @returns {Model<IIncentiveDetails>} The IncentiveDetails model
     */
    public static get model(): Model<IIncentiveDetails> {
        return IncentiveDetails._model;
    }
}