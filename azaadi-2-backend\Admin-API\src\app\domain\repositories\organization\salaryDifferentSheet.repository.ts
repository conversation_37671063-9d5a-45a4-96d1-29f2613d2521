import { IDifferentSalarySheet } from "../../interfaces/organization/salaryReport.interface";
import { SalaryDifferentSheet } from "../../models";
import Repository from "../repository";
import ISalaryDifferentSheetRepository from "./abstracts/salaryDifferentSheetRepository.abstract";

export default class SalaryDifferentSheetRepository extends Repository<IDifferentSalarySheet> implements ISalaryDifferentSheetRepository {
    constructor() {
        super(SalaryDifferentSheet.model);
    }
}