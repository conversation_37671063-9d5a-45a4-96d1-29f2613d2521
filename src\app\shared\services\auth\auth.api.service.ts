import { HttpClient, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ILoginRequest } from 'src/app/modules/login/model/Loging';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AuthApiService {
  // private readonly _baseUrl = environment.apiUrl + '/admin/user';
  private readonly _baseUrl = environment.apiUrl;
  // private readonly _loginUrl = this._baseUrl + '/login';
  private readonly _loginUrl = this._baseUrl + '/auth';
  private readonly _http = inject(HttpClient);

  login(data: ILoginRequest): Observable<HttpResponse<any>> {
    debugger;
    return this._http.post<any>(this._loginUrl, data, { observe: 'response' });
  }
}
