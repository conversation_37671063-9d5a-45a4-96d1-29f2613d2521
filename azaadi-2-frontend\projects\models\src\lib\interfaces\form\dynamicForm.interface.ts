import { <PERSON><PERSON><PERSON><PERSON>, ICom<PERSON>Access, ICommonIdUser, ICommonName, ICommonTag, ICommonTileValue } from "../common.interface";

interface STitle extends Partial<Pick<ICommonTileValue,'sTitle'> >{}

interface  SValue {
    sValue?: string; 
}

//  tUser?: string | EmsUser; =>  tIdUser: string | EmsUser;
export interface EmsDynamicForm extends STitle, Partial<ICommonTag>,ICommonAccess,ICommonIdUser{
    sBgUrl?: string;
    sContent?: string;
    oInputGroups?: EmsFormGroup[];
   
}

export interface EmsFormGroup extends STitle {
    oGroupRows?: EmsGroupRows[];
}
export interface  EmsGroupRows extends STitle{
    oInputFields?: EmsInputField[];
}

export interface EmsInputField extends Pick<ICommon,'_id'>{
    sParentId?: string;
    sLabel?: string;
    sInputType?: string;
    bIsR<PERSON>only?: boolean;
    bIsDisabled?: boolean;
    bIsMandatory?: boolean;
    sPlaceholder?: string;
    sIconUrl?: string;
    sTooltips?: string;
}

/**
 * email / text / search / password
 *
 * @export
 * @interface EmsInputFieldText
 * @extends {EmsInputField}
 */
export interface EmsInputFieldText extends EmsInputField,SValue {
    sDefaultValue?: string;
}

/**
 * number / tel /range
 *
 * @export
 * @interface EmsInputFieldNumber
 * @extends {EmsInputField}
 */
export interface EmsInputFieldNumber extends EmsInputField {
    aDefaultValue?: number;
    aMin?: number;
    aMean?: number;
    aMax?: number;
}

/**
 * Date / Date-Time / Time
 *
 * @export
 * @interface EmsInputFieldDate
 * @extends {EmsInputField}
 */
export interface EmsInputFieldDate extends EmsInputField {
    sDefaultValue?: Date;
    sValue?: Date;
}

/**
 * checkbox / radio
 *
 * @export
 * @interface EmsInputFieldCheck
 * @extends {EmsInputField}
 */
export interface EmsInputFieldCheck extends EmsInputField {
    sDefaultValue?: string;
}

/**
 * Select
 *
 * @export
 * @interface EmsInputFieldSelect
 * @extends {EmsInputField}
 */
export interface EmsInputFieldSelect extends EmsInputField {
    sDefaultValue?: string;
    oOption: EmsSelectOption[];
}

export interface EmsSelectOption extends EmsInputField,SValue,Partial< Pick<ICommonName,'_id' | 'sName'>> {
}
