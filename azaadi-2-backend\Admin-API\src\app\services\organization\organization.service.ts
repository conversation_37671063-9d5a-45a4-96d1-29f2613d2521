import { IOrganization } from "../../domain/interfaces/organization/organization.interface";
import IOrganizationRepository from "../../domain/repositories/organization/abstracts/organizationRepository.abstract";
import OrganizationRepository from "../../domain/repositories/organization/organization.repository";
import Service from "../service";
import IOrganizationService from "./abstracts/organizationService.abstract";

export default class OrganizationService extends Service<IOrganization, IOrganizationRepository> implements IOrganizationService {
    constructor() {
        super(new OrganizationRepository());
    }
}