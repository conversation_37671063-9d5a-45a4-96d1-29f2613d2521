import { CommonModule } from '@angular/common';
import { Component, Input, ViewChild } from '@angular/core';
import { IUser } from '@shared/models';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { Popover, PopoverModule } from 'primeng/popover';
import { BadgeComponent } from '../badge/badge.component';
import { DialogModule } from 'primeng/dialog';

@Component({
  selector: 'app-users-tray',
  imports: [
    CardModule,
    PopoverModule,
    ButtonModule,
    DividerModule,
    CommonModule,
    BadgeComponent,
    DialogModule,
  ],
  templateUrl: './users-tray.component.html',
})
export class UsersTrayComponent {
  @Input() users: IUser[] = [];
  @Input() heading: string = '';
  @Input() colCount!: number;
  @Input() icon!: string;
  @Input() noUserImage!: string;
  @Input() noUserText!: string;
  @Input() bgColor!: string;
  @ViewChild('popover') popover!: Popover;
  totalCols = this.colCount + 1;
  selectedUser: IUser | undefined;
  showAllUsers = false;

  displayUser(event: any, user: IUser) {
    if (this.selectedUser?.name === user.name) {
      this.popover.hide();
      this.selectedUser = undefined;
    } else {
      this.selectedUser = user;
      this.popover.show(event);
      if (this.popover.container) {
        this.popover.align();
      }
    }
  }

  hidePopover() {
    this.popover.hide();
  }

  otherInformation = [
    { label: 'Location', value: 'Hooghly' },
    { label: 'Work Phone', value: '8336099897' },
    { label: 'Job Title', value: 'Software Engineer' },
    { label: 'Secondary Job Title', value: '-Not Set-' },
    { label: 'Department', value: 'Development' },
    { label: 'Bussiness Unit', value: '-Not Set-' },
  ];
}
