<div class="flex m-auto">
  <div>
    <div *ngFor="let node of data(); let i = index" class="relative">
      <div class="box" [ngClass]="getNodeClasses(node, i)">
        <div class="flex items-center justify-between gap-4 w-full">
          <div
            class="flex gap-2 items-center flex-1"
            (click)="updateCurrentActive(node, i)"
          >
            <img
              [src]="node.image"
              class="h-12 w-12 rounded-full object-cover"
              alt=""
            />
            <div>
              <p class="text-sm font-semibold">{{ node.name }}</p>
              <p class="text-xs">
                {{ node.designation }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <span
        (click)="updateCurrentActive(node, i)"
        *ngIf="node.children?.length"
        [ngClass]="{ activeChild: node.expanded }"
        class="child"
        >{{ node.children?.length }}</span
      >
    </div>
  </div>

  <div *ngFor="let node of data(); let i = index">
    <ng-container *ngIf="node.expanded && node.children">
      <app-tree-view
        [parentIndex]="i"
        [data]="node.children"
        [depth]="depth() + 1"
      ></app-tree-view>
    </ng-container>
  </div>
</div>
