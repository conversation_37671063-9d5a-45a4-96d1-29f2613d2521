import { Pipe, PipeTransform } from '@angular/core';
import { TicketDepartment } from '../models/ticket';
import { TagSeverity } from '@shared/models/types/tag-serverity';

@Pipe({
  name: 'departmentColor'
})
export class DepartmentColorPipe implements PipeTransform {

  transform(status: TicketDepartment, ...args: unknown[]): TagSeverity {
    switch (status) {
      case TicketDepartment.IT_DEPARTMENT:
        return 'warn';
      case TicketDepartment.DEV_DEPARTMENT:
        return 'success';
      case TicketDepartment.HR_DEPARTMENT:
        return 'info';
      case TicketDepartment.FINANCE_DEPARTMENT:
        return 'danger';
      default:
        return 'secondary';
    }
  }

}
