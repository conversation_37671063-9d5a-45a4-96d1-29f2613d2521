import {
  ITicket,
  TicketStatus,
  TicketPriority,
  TicketDepartment,
} from 'src/app/modules/home/<USER>/ticket/models/ticket';

export const TICKETS_DUMMY_DATA: ITicket[] = [
  {
    id: 1,
    title: 'Printer not working',
    description:
      'The printer located in the finance department has stopped functioning since yesterday afternoon. Attempts to restart it have failed, and it is not being detected by any system on the network. We suspect either a hardware issue or a driver conflict. Immediate attention is required as it is impacting report printing for the quarterly review.',
    status: TicketStatus.OPEN,
    priority: TicketPriority.HIGH_PRIORITY,
    createdAt: new Date('2025-04-01T09:15:00'),
    department: TicketDepartment.IT_DEPARTMENT,
    assignedTo: {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/10.jpg',
    },
    raisedBy: {
      firstName: 'Jane',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/10.jpg',
    },
    comments: [],
    attachments: [
      'https://example.com/uploads/printer-error-photo.jpg',
      'https://example.com/uploads/device-diagnostics-report.pdf',
    ],
  },
  {
    id: 2,
    title: 'New software installation request',
    description:
      'The design team has requested the installation of Adobe Photoshop on their workstations to support upcoming campaign material preparation. They are currently relying on outdated tools that are not compatible with the new file formats from our marketing partners. This request needs to be addressed before the next design sprint starts.',
    status: TicketStatus.OPEN,
    priority: TicketPriority.MEDIUM_PRIORITY,
    createdAt: new Date('2025-04-05T10:45:00'),
    department: TicketDepartment.FINANCE_DEPARTMENT,
    assignedTo: {
      firstName: 'Olivia',
      lastName: 'Lee',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/20.jpg',
    },
    raisedBy: {
      firstName: 'Mark',
      lastName: 'Taylor',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/20.jpg',
    },
    comments: [],
    attachments: ['https://example.com/uploads/software-request-form.pdf'],
  },
  {
    id: 3,
    title: 'Network outage in 3rd floor',
    description:
      'Users on the entire third floor are experiencing network connectivity issues since this morning. Neither wired nor wireless connections are functioning, and restarting the routers did not resolve the issue. This has completely halted work for the support and legal departments. Please investigate and resolve urgently.',
    status: TicketStatus.OPEN,
    priority: TicketPriority.HIGH_PRIORITY,
    createdAt: new Date('2025-04-10T08:00:00'),
    department: TicketDepartment.IT_DEPARTMENT,
    assignedTo: {
      firstName: 'Lucas',
      lastName: 'Kim',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/30.jpg',
    },
    raisedBy: {
      firstName: 'Sophia',
      lastName: 'Williams',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/30.jpg',
    },
    comments: [],
    attachments: [
      'https://example.com/uploads/network-floor-plan.pdf',
      'https://example.com/uploads/network-issue-screenshot.png',
    ],
  },
  {
    id: 4,
    title: 'Laptop request for new employee',
    description:
      'A new software developer joining next week requires a laptop configured with all development tools and access credentials. The laptop should have at least 16GB RAM, an SSD drive, and all necessary software including Node.js, VS Code, and Docker. Please ensure setup is completed by Friday.',
    status: TicketStatus.CLOSED,
    priority: TicketPriority.MEDIUM_PRIORITY,
    createdAt: new Date('2025-04-03T14:25:00'),
    department: TicketDepartment.DEV_DEPARTMENT,
    assignedTo: {
      firstName: 'Ethan',
      lastName: 'Nguyen',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/40.jpg',
    },
    raisedBy: {
      firstName: 'Emily',
      lastName: 'Clark',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/40.jpg',
    },
    comments: [],
    attachments: ['https://example.com/uploads/laptop-request-spec-sheet.pdf'],
  },
  {
    id: 5,
    title: 'HR system login issue',
    description:
      'Several employees are unable to log into the HR portal since the last update was rolled out. The system either throws a timeout error or redirects to the login page after entering credentials. This is preventing users from accessing payslips and leave applications.',
    status: TicketStatus.OPEN,
    priority: TicketPriority.LOW_PRIORITY,
    createdAt: new Date('2025-04-07T11:10:00'),
    department: TicketDepartment.HR_DEPARTMENT,
    assignedTo: {
      firstName: 'Noah',
      lastName: 'Perez',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/50.jpg',
    },
    raisedBy: {
      firstName: 'Ava',
      lastName: 'Martinez',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/50.jpg',
    },
    comments: [],
    attachments: ['https://example.com/uploads/hr-login-error-screenshot.png'],
  },
  {
    id: 6,
    title: 'Air conditioning issue in support office',
    description:
      'The air conditioning in the customer support office has stopped working since last evening. The room is becoming uncomfortable, especially during afternoon hours. This might affect team productivity and comfort if not resolved quickly.',
    status: TicketStatus.OPEN,
    priority: TicketPriority.MEDIUM_PRIORITY,
    createdAt: new Date('2025-04-08T16:30:00'),
    department: TicketDepartment.HR_DEPARTMENT,
    assignedTo: {
      firstName: 'Grace',
      lastName: 'Patel',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/60.jpg',
    },
    raisedBy: {
      firstName: 'Liam',
      lastName: 'Brown',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/60.jpg',
    },
    comments: [],
    attachments: [
      'https://example.com/uploads/ac-issue-report.pdf',
      'https://example.com/uploads/room-temperature-graph.jpg',
    ],
  },
];
