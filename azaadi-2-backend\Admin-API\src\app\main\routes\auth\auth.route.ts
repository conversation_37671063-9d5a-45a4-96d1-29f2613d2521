import { <PERSON><PERSON><PERSON><PERSON>, Router } from "express";
import AsyncUtils from "../../../../utils/async.utils";
import AuthController from "../../controllers/auth/auth.controller";

export default class AuthRoutes {
    public routes: IRouter;
    private _authController: AuthController;
    constructor() {
        this._authController=new AuthController();
        this.routes = Router();
        this._initializeRoutes();
    }

    private _initializeRoutes(): void {
        this.routes.route("/").post(AsyncUtils.wrapHandler(this._authController.authenticateUser.bind(this._authController)));
    }
}