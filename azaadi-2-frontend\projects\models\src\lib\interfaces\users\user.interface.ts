import { EmsFileType } from '../../enums/managements.enum';
import {
  EmsAttendanceStatus,
  EmsAttendanceType,
  EmsUserLeaveStatus,
  EmsUserRequestStatus,
} from '../../enums/users.enum';
import {
  EmsBranches,
  EmsDepartments,
  EmsDesignation,
  EmsManageAssets,
  EmsManageDocuments,
  EmsManageLeave,
  EmsManageUserRequest,
  EmsRole,
  EmsShift,
} from '../organization/manageOrganization.interface';
import {
  EmsSalaryStructure,
  EmsUpdatedSalaryStructure,
} from '../salary/salary.interface';
import {
  ICommonAttachment,
  ICommonDetails,
  ICommonUser,
  ICommonIdUserWithName,
  ICommonIdWithArrSEDate,
  ICommonLeave,
  ICommonName,
  ICommonRequest,
  ICommonStartEndDate,
  ICommonSupperAdmin,
  ICommonUserLog,
  ICommonWithSEDate,
} from '../common.interface';
import { EmsEmployeeCode } from '../employee-code/employeeCode.interface';

/**
 * User Doc
 *
 * @export
 * @interface EmsUser
 */
export interface EmsUser extends ICommonUser {
  sWorkingType?: string;
  tBranches?: EmsBranches[] | string[];
  tIdEmployee?: EmsEmployeeCode | string;
  tUserDetails?: EmsUserDetails;
  tRole: EmsRole | string;
  bOnlyOfficePunch?: Boolean;
  tShift?: EmsShift[] | string[];
  bIsApplicableAdvance?: boolean;
  dInactiveDate?: Date;
  bIsResigned?: Boolean;
}
/**
 * User Details Doc
 *
 * @export
 * @interface EmsUserDetails
 */
export interface EmsUserDetails extends ICommonDetails {
  tDepartment?: string | EmsDepartments;
  tDesignation?: string | EmsDesignation;
  aAltPhoneNo?: number;
  sPersonalEmail?: string;
  sGender?: string;
  dDob?: Date;
  dJoinDate?: Date;
  sPresentAddress?: string;
  sPermanentAddress?: string;
  sFatherName?: string;
  aFatherMobileNo?: number;
  aAadharNo?: number;
  sPanNo?: string;
  tEducationInfo?: EmsEducationInfo[];
  tExperienceInfo?: EmsExperienceInfo[];
  sPassportNo?: string;
  sPassportExpDate?: Date;
  sNationality?: string;
  sReligion?: string;
  sMaritalStatus?: string;
  sEmploymentSpouse?: string;
  aNoChildren?: number;
  sUserApplyType?: string;
  metaData?: string[];
  tSalaryStructure?: EmsSalaryStructure | string;
  tUpdatedSalaryStructure?: EmsUpdatedSalaryStructure | string;
  dLeaveDate?: Date;
}

export interface EmsUserLog extends ICommonUserLog {
  sMetaKey: string;
  sMetaValue: string;
}

export interface EmsUserDocuments extends ICommonIdUserWithName {
  tManageDoc: string | EmsManageDocuments;
  tDocumentDetails?: EmsUserDocumentDetails[];
}

export interface EmsUserDocumentDetails extends ICommonName {
  sSize?: string;
  sUrl?: string;
  eType?: EmsFileType;
  bStatus?: boolean;
}

export interface EmsUserAssets extends ICommonIdUserWithName {
  tManageAsset: string[] | EmsManageAssets[];
}

export interface EmsEducationInfo extends ICommonWithSEDate {
  sCourse?: string;
  bPursuing?: boolean;
  aTotalMarks?: number;
  aObtainMarks?: number;
}

export interface EmsExperienceInfo extends ICommonWithSEDate {
  sDepartment?: string;
  sDesignation?: string;
  sAddress?: string;
  bCurrent?: boolean;
}

export interface EmsUserAttendance extends ICommonIdWithArrSEDate {
  eAttendanceType?: EmsAttendanceType;
  eEndAttendanceType?: EmsAttendanceType;
  eStatus?: EmsAttendanceStatus;
  tShift?: EmsShift | string;
  sLongitude?: string;
  sLatitude?: string;
  aLateDuration?: number; // minute
  sWorkingHours?: string;
}

export interface EmsUserLeaves extends ICommonLeave {
  tLeaveType?: string | EmsManageLeave;
  tCustomIdUser?: string[] | EmsUser[];
  aLeaveCount?: number;
  eStatus?: EmsUserLeaveStatus;
}

export interface EmsUserBankDetails extends ICommonUserLog {
  sBankName?: string;
  sBankAccount?: string;
  sIfscCode?: string;
  sBranchName?: string;
  bIsActive?: Boolean;
}

export interface EmsUserRequest extends ICommonRequest {
  tSender: EmsUser | string;
  tType: EmsManageUserRequest | string;
  eStatus: EmsUserRequestStatus;
  sReply?: string;
  tRecipients: EmsUser[] | string[];
  aCount: number;
  bIsHalfDay?: boolean;
  tAttachments?: ICommonAttachment[];
  dMultipleDates?: ICommonStartEndDate[];
}

export interface EmsSuperAdmin extends ICommonSupperAdmin { }
