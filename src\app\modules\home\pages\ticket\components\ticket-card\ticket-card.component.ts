import { CommonModule } from '@angular/common';
import { Component, input } from '@angular/core';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ITicket } from '../../models/ticket';
import { DepartmentColorPipe } from '../../pipes/department-color.pipe';
import { TicketPriorityColorPipe } from '../../pipes/ticket-priority-color.pipe';

@Component({
  selector: 'app-ticket-card',
  imports: [CommonModule, TagModule, CardModule, DepartmentColorPipe, TicketPriorityColorPipe, TooltipModule],
  templateUrl: './ticket-card.component.html',
  styleUrl: './ticket-card.component.scss'
})
export class TicketCardComponent {
  ticket = input.required<ITicket>();
  isSelected = input<boolean>(false);
}
