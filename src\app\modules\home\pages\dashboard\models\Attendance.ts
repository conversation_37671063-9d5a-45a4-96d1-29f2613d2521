export enum AttendanceStatus {
  PRESENT = 'Present',
  Leave = 'Leave',
  PUBLIC_HOLIDAY = 'Public Holiday',
  ORGANIZATION_WEEKEND = 'Organization Weekend',
  ERROR = 'Error',
  TODAYS_DATE = "Today's Date",
  LHD = 'Less Than Half Day',
  HD = 'Half Day',
}

export interface ITodaysAttendance {
  dStartDate: Date; // Start of attendance
  dEndDate: Date; // End of attendance
  eAttendanceType: AttendanceType; // Attendance type enum/id (e.g., Present, Absent, etc.)
  eEndAttendanceType: AttendanceType; // End attendance type (if differs from start)
  tShift: string; // Shift identifier or name
}

export interface ITodaysAttendanceResponse {
  dStartDate: Date[]; // List of start dates
  dEndDate: Date[]; // List of end dates
  eAttendanceType: AttendanceType; // Common attendance type
  eEndAttendanceType: AttendanceType; // Common end attendance type
  tShift: string; // Common shift across all dates
}

export enum AttendanceType {
  ONLINE = 0,
  OFFLINE = 1,
}
