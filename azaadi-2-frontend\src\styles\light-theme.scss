// This file was generated by running 'ng generate @angular/material:m3-theme'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '@angular/material' as mat;

// Note: Color palettes are generated from primary: #233857
$_palettes: (
  primary: (
    0: #000000,
    10: #001b3c,
    20: #003061,
    25: #003b74,
    30: #004788,
    35: #0e5299,
    40: #235fa6,
    50: #4278c1,
    60: #5e92dd,
    70: #7aadfa,
    80: #a7c8ff,
    90: #d5e3ff,
    95: #ebf1ff,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #121c2b,
    20: #273141,
    25: #323c4d,
    30: #3d4758,
    35: #495364,
    40: #555f71,
    50: #6e778a,
    60: #8791a5,
    70: #a2abc0,
    80: #bdc7dc,
    90: #d9e3f8,
    95: #ebf1ff,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #27132f,
    20: #3e2845,
    25: #4a3351,
    30: #563e5d,
    35: #624a69,
    40: #6e5676,
    50: #886e8f,
    60: #a387aa,
    70: #bfa2c5,
    80: #dbbce1,
    90: #f8d8fe,
    95: #feebff,
    98: #fff7fb,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1a1c1e,
    20: #2f3033,
    25: #3a3b3e,
    30: #46474a,
    35: #525256,
    40: #5e5e62,
    50: #76777a,
    60: #909094,
    70: #ababaf,
    80: #c7c6ca,
    90: #e3e2e6,
    95: #f1f0f4,
    98: #faf9fd,
    99: #fdfbff,
    100: #ffffff,
    4: #0d0e11,
    6: #121316,
    12: #1e2023,
    17: #292a2d,
    22: #343538,
    24: #38393c,
    87: #dad9dd,
    92: #e9e7eb,
    94: #eeedf1,
    96: #f4f3f7,
  ),
  neutral-variant: (
    0: #000000,
    10: #181c22,
    20: #2d3038,
    25: #383b43,
    30: #43474e,
    35: #4f525a,
    40: #5b5e66,
    50: #74777f,
    60: #8e9199,
    70: #a8abb4,
    80: #c4c6cf,
    90: #e0e2ec,
    95: #eef0fa,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);
$_primary: map.merge(map.get($_palettes, primary), $_rest);
$_tertiary: map.merge(map.get($_palettes, tertiary), $_rest);

$light-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: $_primary,
    tertiary: $_tertiary,
  ),
));
.light {
  @include mat.all-component-themes($light-theme);
  
  
   // colors
    --color-primary: #233857;
    --text-color-primary:#ffffff;
    --color-secondary:#121c2b;
    --text-color-secondary:#f6faff;

  //background
  // --mat-sidenav-content-background-color: #ebf3ffc9;
  --mat-toolbar-container-background-color:var( --color-primary);
  --mat-toolbar-container-text-color:var(--text-color-primary);

  .mat-toolbar .mat-mdc-icon-button .mat-icon{
    color:var(--text-color-primary);
  }
 
  // sidebar
  --mat-sidenav-container-background-color: #eeedf1;
  --mat-expansion-container-background-color: #eeedf1;
  // --mat-sidenav-container-text-color:#ffffff;
  // --mdc-list-list-item-label-text-color:#ffffff;
  // --mat-expansion-header-text-color:#ffffff;
  // --mdc-list-list-item-hover-label-text-color:#ffffff;
  // --mdc-list-list-item-focus-label-text-color:#ffffff;
  // --mdc-list-list-item-hover-state-layer-color:#696968;
  // --mdc-list-list-item-focus-state-layer-color:#a8a69c;
  // --mdc-list-list-item-hover-state-layer-opacity:0.3;
  // --mdc-list-list-item-focus-state-layer-opacity:0.5;

  --mat-sidenav-container-width:250px;
  --mat-expansion-container-shape:0;
  --mat-divider-color:#cdced3;
}