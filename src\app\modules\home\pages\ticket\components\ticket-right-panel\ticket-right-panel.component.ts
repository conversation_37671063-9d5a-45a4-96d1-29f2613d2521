import { Component, input } from '@angular/core';
import { ITicket } from '../../models/ticket';
import { TagModule } from 'primeng/tag';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { DividerModule } from 'primeng/divider';
import { FileDownBtnComponent } from "../file-down-btn/file-down-btn.component";
import { DepartmentColorPipe } from '../../pipes/department-color.pipe';
import { TicketPriorityColorPipe } from '../../pipes/ticket-priority-color.pipe';

@Component({
  selector: 'app-ticket-right-panel',
  imports: [TagModule, ButtonModule, CommonModule, DividerModule, FileDownBtnComponent, DepartmentColorPipe, TicketPriorityColorPipe],
  templateUrl: './ticket-right-panel.component.html',
  styleUrl: './ticket-right-panel.component.scss'
})
export class TicketRightPanelComponent {
  ticket = input.required<ITicket>();
}
