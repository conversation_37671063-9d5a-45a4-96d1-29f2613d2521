import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { CardModule } from 'primeng/card';
import { ELeaveType, ILeaveBalance } from '../../../../models/leave';
import { CircularProgressComponent } from '../../../../../../../../shared/components/circular-progress/circular-progress.component';
import { ButtonModule } from 'primeng/button';
import { TranslatePipe } from '@ngx-translate/core';
import { LeaveBalanceService } from '../../../../services/leavebalance/leavebalance.service';
import { LeaveBalanceApiService } from '../../../../services/leavebalance/leavebalance.api.service';
import { ProgressSpinner } from 'primeng/progressspinner';

@Component({
  selector: 'app-leave-balances',
  imports: [
    CardModule,
    CommonModule,
    CircularProgressComponent,
    ButtonModule,
    TranslatePipe,
    ProgressSpinner,
  ],
  providers: [LeaveBalanceService, LeaveBalanceApiService],
  templateUrl: './leave-balances.component.html',
})
export class LeaveBalancesComponent implements OnInit {
  leaveBalances: ILeaveBalance[] = [];
  isLoading = true;

  leaveTypeNames = ['Sick Leave', 'Annual Leave', 'Casual Leave'];

  private _leaveBalanceService = inject(LeaveBalanceService);

  ngOnInit(): void {
    this._leaveBalanceService.fetchLeaveBalance().subscribe({
      next: (data) => {
        this.leaveBalances = data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching leave balances:', error);
        this.isLoading = false;
      },
    });
  }
  getLeaveTypeName(leaveType: number): string {
    switch (leaveType) {
      case ELeaveType.AnnualLeave:
        return 'Annual Leave';
      case ELeaveType.SickLeave:
        return 'Sick Leave';
      case ELeaveType.CasualLeave:
        return 'Casual Leave';
      default:
        return 'Unknown Leave Type';
    }
  }
}
