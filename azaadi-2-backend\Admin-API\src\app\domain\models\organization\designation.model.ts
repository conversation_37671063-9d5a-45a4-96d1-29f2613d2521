import { model, Model, models, Schema } from "mongoose";
import { IDesignation } from "../../interfaces/organization/department.interface";

/**
 * Designation model class that defines the schema and provides access to the Designation collection.
 * 
 * @class Designation
 * @description Handles the schema definition and model creation for Designations
 */
export default class Designation {
    /**
     * Mongoose schema definition for Designation
     * @private
     */
    private static _schema = new Schema<IDesignation>(
        {
            sName: {
                type: String,
                required: [true, 'Name is missing!'],
                trim: true,
                index: true // Indexed for faster search and filtering by designation name
            },
            sTag: {
                type: String,
                trim: true,
                index: true // Indexed for quick filtering by designation tag
            },
            tOrganization: {
                type: Schema.Types.ObjectId,
                ref: 'organization',
                required: [true, 'Organization is missing!'],
                trim: true,
                index: true // Indexed for faster lookups and joins by organization
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for Designation
     * @private
     */
    private static _model: Model<IDesignation> = models?.Designation || model<IDesignation>("designation", Designation._schema);

    /**
     * Get the Mongoose model for Designation
     * @returns {Model<IDesignation>} The Designation model
     */
    public static get model(): Model<IDesignation> {
        return Designation._model;
    }
}