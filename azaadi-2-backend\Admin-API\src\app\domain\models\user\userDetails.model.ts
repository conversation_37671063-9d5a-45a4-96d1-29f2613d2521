import { model, Model, models, Schema } from "mongoose";
import { IUserDetails } from "../../interfaces/user/user.interface";
import { IEducationInfo, IExperienceInfo } from "../../interfaces/user/user.interface";
import { EMSGender, EMSMaritalStatus, EMSWorkingType } from "../../../../utils/meta/enum.utils";

/**
 * UserDetails model class that defines the schema and provides access to the UserDetails collection.
 * 
 * @class UserDetails
 * @description Handles the schema definition and model creation for User Details
 */
export default class UserDetails {
    /**
     * Mongoose schema definition for education information
     * @private
     */
    private static readonly _educationInfoSchema:Schema<IEducationInfo> = new Schema<IEducationInfo>(
        {
            sName: {
                type: String,
                trim: true
            },
            sCourse: {
                type: String,
                trim: true
            },
            dStartDate: {
                type: Date,
                trim: true
            },
            dEndDate: {
                type: Date,
                trim: true
            },
            bPursuing: {
                type: Boolean
            },
            aTotalMarks: {
                type: Number,
                trim: true
            },
            aObtainMarks: {
                type: Number,
                trim: true
            }
        },
        { timestamps: true }
    );

    /**
     * Mongoose schema definition for experience information
     * @private
     */
    private static readonly _experienceInfoSchema:Schema<IExperienceInfo> = new Schema<IExperienceInfo>(
        {
            sName: {
                type: String,
                trim: true
            },
            sDepartment: {
                type: String,
                trim: true
            },
            sDesignation: {
                type: String,
                trim: true
            },
            sAddress: {
                type: String,
                trim: true
            },
            dStartDate: {
                type: Date,
                trim: true
            },
            dEndDate: {
                type: Date,
                trim: true
            },
            bCurrent: {
                type: Boolean
            }
        },
        { timestamps: true }
    );

    /**
     * Mongoose schema definition for UserDetails
     * @private
     */
    private static readonly _schema:Schema<IUserDetails> = new Schema<IUserDetails>(
        {
            sName: {
                type: String,
                required: [true, "Name must be required!"],
                trim: true,
                index: true // Indexed for faster search and filtering by name
            },
            aAltPhoneNo: {
                type: Number,
                min: [1000000000, "Phone number must be 10 digits"],
                max: [9999999999, "Phone number must be 10 digits"],
                trim: true
            },
            sPersonalEmail: {
                type: String,
                trim: true,
                match: [/^[\w-\.]+@([\w-]+)\.+[\w-]{2,4}$/, 'Please provide a valid email address'],
                index: true // Indexed for faster search and filtering by email
            },
            sGender: {
                type: String,
                trim: true,
                enum: EMSGender
            },
            dDob: {
                type: Date,
                trim: true
            },
            dJoinDate: {
                type: Date,
                trim: true,
                index: true // Indexed for date-based queries
            },
            dLeaveDate: {
                type: Date,
                trim: true,
                index: true // Indexed for date-based queries
            },
            sPresentAddress: {
                type: String,
                trim: true
            },
            sPermanentAddress: {
                type: String,
                trim: true
            },
            sFatherName: {
                type: String,
                trim: true
            },
            aFatherMobileNo: {
                type: Number,
                trim: true
            },
            aAadharNo: {
                type: Number,
                trim: true
            },
            sPanNo: {
                type: String,
                trim: true
            },
            sPassportNo: {
                type: String,
                trim: true
            },
            sPassportExpDate: {
                type: Date,
                trim: true
            },
            sNationality: {
                type: String,
                trim: true
            },
            sReligion: {
                type: String,
                trim: true
            },
            sMaritalStatus: {
                type: String,
                trim: true,
                enum: EMSMaritalStatus
            },
            sEmploymentSpouse: {
                type: String,
                trim: true
            },
            aNoChildren: {
                type: Number,
                trim: true
            },
            bIsActive: {
                type: Boolean,
                default: true,
                index: true // Indexed for active/inactive filtering
            },
            sUserApplyType: {
                type: String,
                trim: true,
                enum: EMSWorkingType,
                index: true // Indexed for user apply type filtering
            },
            tEducationInfo: {
                type: [UserDetails._educationInfoSchema]
            },
            tExperienceInfo: {
                type: [UserDetails._experienceInfoSchema]
            },
            metaData: {
                type: [String],
                trim: true
            },
            tSalaryStructure: {
                type: Schema.Types.ObjectId,
                trim: true,
                ref: "salarystructure",
                index: true // Indexed for salary structure lookups
            },
            tUpdatedSalaryStructure: {
                type: Schema.Types.ObjectId,
                trim: true,
                ref: "updatedsalarystructure",
                index: true // Indexed for updated salary structure lookups
            },
            sPfNo: {
                type: String,
                trim: true,
                index: true // Indexed for PF number lookups
            },
            sEsiNo: {
                type: String,
                trim: true,
                index: true // Indexed for ESI number lookups
            },
            sMotherName: {
                type: String,
                trim: true
            },
            aEmergencyContactNo: {
                type: Number
            },
            sEmergencyContactName: {
                type: String,
                trim: true
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents
        }
    );

    /**
     * Mongoose model for UserDetails
     * @private
     */
    private static _model: Model<IUserDetails> = models?.userdetails || model<IUserDetails>("userdetails", UserDetails._schema);

    /**
     * Get the Mongoose model for UserDetails
     * @returns {Model<IUserDetails>} The UserDetails model
     */
    public static get model(): Model<IUserDetails> {
        return UserDetails._model;
    }
}