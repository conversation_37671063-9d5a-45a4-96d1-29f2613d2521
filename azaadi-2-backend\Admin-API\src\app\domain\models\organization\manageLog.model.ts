import { model, Model, models, Schema } from "mongoose";
import { IManageLog } from "../../interfaces/organization/manageLog.interface";

/**
 * ManageLog model class that defines the schema and provides access to the ManageLog collection.
 * 
 * @class ManageLog
 * @description Handles the schema definition and model creation for Organization Management Logs
 */
export default class ManageLog {
    /**
     * Mongoose schema definition for ManageLog
     * @private
     */
    private static _schema: Schema<IManageLog> = new Schema<IManageLog>(
        {
            tIdUser: {
                type: Schema.Types.ObjectId,
                required: [true, 'Author signature is missing!'],
                ref: 'login',
                trim: true,
                index: true // Indexed for faster lookups by user
            },
            sMetaKey: {
                type: [String],
                required: [true, 'Key is missing!'],
                trim: true
            },
            sMetaValue: {
                type: [String],
                required: [true, 'Value is missing!'],
                trim: true
            }
        },
        {
            timestamps: true,
            versionKey: false // Disable the __v field in documents to avoid unnecessary version tracking
        }
    );

    /**
     * Mongoose model for ManageLog
     * @private
     */
    private static _model: Model<IManageLog> = models?.ManageLog || model<IManageLog>("orgManageLog", ManageLog._schema);

    /**
     * Get the Mongoose model for ManageLog
     * @returns {Model<IManageLog>} The ManageLog model
     */
    public static get model(): Model<IManageLog> {
        return ManageLog._model;
    }
}